-- Simple script to complete essential database structure updates
-- This script handles the core requirements without complex indexing

-- Start transaction
START TRANSACTION;

-- Create a simple mapping table for sections
CREATE TABLE IF NOT EXISTS simple_sections (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(10) NOT NULL UNIQUE,
  display_name <PERSON><PERSON><PERSON><PERSON>(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Populate simple_sections with existing section data from students table
INSERT IGNORE INTO simple_sections (name, display_name)
SELECT DISTINCT section, CONCAT('Section ', section)
FROM students 
WHERE section IS NOT NULL AND section != '';

-- Update section_id based on section name (using simple_sections table)
UPDATE students s
JOIN simple_sections ss ON s.section = ss.name
SET s.section_id = ss.id
WHERE s.section_id IS NULL AND s.section IS NOT NULL;

-- Add foreign key constraint for section_id if it doesn't exist
SET @fk_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
  WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'students' 
  AND CONSTRAINT_NAME = 'fk_student_simple_section'
);

SET @sql = IF(@fk_exists = 0, 
  'ALTER TABLE students ADD CONSTRAINT fk_student_simple_section FOREIGN KEY (section_id) REFERENCES simple_sections(id)',
  'SELECT "section_id foreign key already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create a comprehensive view for student final marks
CREATE OR REPLACE VIEW student_final_marks AS
SELECT 
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.father_name,
    s.mother_name,
    s.class AS class_name,
    s.section AS section_name,
    s.trade AS trade_name,
    s.stream AS stream_name,
    s.session AS academic_session,
    -- Subject and exam information
    sub.name AS subject_name,
    sub.code AS subject_code,
    e.exam_name,
    e.exam_id,
    -- Marks breakdown
    ssm.theory_marks,
    ssm.practical_marks,
    ssm.internal_marks,
    ssm.total_marks,
    ssm.max_marks,
    ssm.percentage,
    ssm.grade,
    ssm.is_pass,
    -- Additional calculated fields
    CASE 
        WHEN ssm.percentage >= 90 THEN 'A+'
        WHEN ssm.percentage >= 80 THEN 'A'
        WHEN ssm.percentage >= 70 THEN 'B+'
        WHEN ssm.percentage >= 60 THEN 'B'
        WHEN ssm.percentage >= 50 THEN 'C+'
        WHEN ssm.percentage >= 40 THEN 'C'
        WHEN ssm.percentage >= 33 THEN 'D'
        ELSE 'F'
    END AS calculated_grade,
    CASE 
        WHEN ssm.percentage >= 33 THEN 'PASS'
        ELSE 'FAIL'
    END AS result_status
FROM 
    students s
    LEFT JOIN subjects sub ON 1=1  -- We'll need to establish proper subject relationships
    LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND sub.id = ssm.subject_id
    LEFT JOIN exams e ON ssm.exam_id = e.exam_id
WHERE 
    ssm.id IS NOT NULL  -- Only show records where marks exist
ORDER BY 
    s.student_id, sub.name, e.exam_name;

-- Create a summary view for student performance
CREATE OR REPLACE VIEW student_performance_summary AS
SELECT 
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.class AS class_name,
    s.section AS section_name,
    s.trade AS trade_name,
    s.session AS academic_session,
    COUNT(DISTINCT ssm.subject_id) AS total_subjects,
    COUNT(DISTINCT CASE WHEN ssm.is_pass = TRUE THEN ssm.subject_id END) AS subjects_passed,
    COUNT(DISTINCT CASE WHEN ssm.is_pass = FALSE THEN ssm.subject_id END) AS subjects_failed,
    ROUND(AVG(ssm.percentage), 2) AS overall_percentage,
    ROUND(SUM(ssm.total_marks), 2) AS total_marks_obtained,
    ROUND(SUM(ssm.max_marks), 2) AS total_max_marks,
    CASE 
        WHEN COUNT(DISTINCT CASE WHEN ssm.is_pass = FALSE THEN ssm.subject_id END) = 0 
        THEN 'PASS' 
        ELSE 'FAIL' 
    END AS overall_result
FROM 
    students s
    LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
WHERE 
    ssm.id IS NOT NULL
GROUP BY 
    s.id, s.student_id, s.name, s.class, s.section, s.trade, s.session
ORDER BY 
    s.student_id;

-- Commit the transaction
COMMIT;

-- Show success message and summary
SELECT 'Database structure update completed successfully!' AS message;
SELECT 'Created simple_sections table and updated student relationships' AS details;

-- Show summary statistics
SELECT COUNT(*) AS total_students FROM students;
SELECT COUNT(*) AS students_with_section_id FROM students WHERE section_id IS NOT NULL;
SELECT COUNT(*) AS total_sections FROM simple_sections;
SELECT COUNT(*) AS total_subjects FROM subjects;
SELECT COUNT(*) AS total_marks_records FROM student_subject_marks;
