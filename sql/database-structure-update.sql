-- Combined script to update database structure with academic session columns
-- This script will:
-- 1. Create missing tables if they don't exist
-- 2. Add foreign key relationships to student table
-- 3. Create tables for subject categorization (compulsory/elective/optional)
-- 4. Create tables for storing subject-specific exam marks
-- 5. Add academic session columns to all relevant tables

-- Start transaction
START TRANSACTION;

-- Create trades table if it doesn't exist
CREATE TABLE IF NOT EXISTS trades (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  code VARCHAR(20) UNIQUE,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create classes table if it doesn't exist
CREATE TABLE IF NOT EXISTS classes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(50) NOT NULL,
  grade VARCHAR(20) NOT NULL,
  stream_id INT,
  academic_session VARCHAR(20) DEFAULT '2023-2024', -- Added academic session column
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create sections table if it doesn't exist
CREATE TABLE IF NOT EXISTS sections (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(10) NOT NULL,
  display_name VARCHAR(50),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) UNIQUE,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create streams table if it doesn't exist
CREATE TABLE IF NOT EXISTS streams (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add foreign key columns to students table if they don't exist
-- First check if the columns exist
SET @column_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'students' 
  AND COLUMN_NAME = 'trade_id'
);

-- Add trade_id if it doesn't exist
SET @sql = IF(@column_exists = 0, 
  'ALTER TABLE students ADD COLUMN trade_id INT, ADD CONSTRAINT fk_student_trade FOREIGN KEY (trade_id) REFERENCES trades(id)',
  'SELECT "trade_id column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if class_id column exists
SET @column_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'students' 
  AND COLUMN_NAME = 'class_id'
);

-- Add class_id if it doesn't exist
SET @sql = IF(@column_exists = 0, 
  'ALTER TABLE students ADD COLUMN class_id INT, ADD CONSTRAINT fk_student_class FOREIGN KEY (class_id) REFERENCES classes(id)',
  'SELECT "class_id column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if section_id column exists
SET @column_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'students' 
  AND COLUMN_NAME = 'section_id'
);

-- Add section_id if it doesn't exist
SET @sql = IF(@column_exists = 0, 
  'ALTER TABLE students ADD COLUMN section_id INT, ADD CONSTRAINT fk_student_section FOREIGN KEY (section_id) REFERENCES sections(id)',
  'SELECT "section_id column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if stream_id column exists
SET @column_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'students' 
  AND COLUMN_NAME = 'stream_id'
);

-- Add stream_id if it doesn't exist
SET @sql = IF(@column_exists = 0, 
  'ALTER TABLE students ADD COLUMN stream_id INT, ADD CONSTRAINT fk_student_stream FOREIGN KEY (stream_id) REFERENCES streams(id)',
  'SELECT "stream_id column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if academic_session column exists in students table
SET @column_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'students' 
  AND COLUMN_NAME = 'academic_session'
);

-- Add academic_session if it doesn't exist
SET @sql = IF(@column_exists = 0,
  'ALTER TABLE students ADD COLUMN academic_session VARCHAR(20) DEFAULT "2023-2024"',
  'SELECT "academic_session column already exists in students table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create student_classes junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS student_classes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_id INT NOT NULL,
  class_id INT NOT NULL,
  section_id INT,
  stream_id INT,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Academic session column
  is_current BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
  FOREIGN KEY (section_id) REFERENCES sections(id) ON DELETE CASCADE,
  FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE CASCADE,
  UNIQUE KEY (student_id, class_id, section_id, academic_session)
);

-- Create student_subjects junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS student_subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_id INT NOT NULL,
  subject_id INT NOT NULL,
  trade_id INT,
  is_compulsory BOOLEAN DEFAULT TRUE,
  is_elective BOOLEAN DEFAULT FALSE,
  is_additional BOOLEAN DEFAULT FALSE,
  is_optional BOOLEAN DEFAULT FALSE,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Academic session column
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
  FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE SET NULL,
  UNIQUE KEY (student_id, subject_id, academic_session)
);

-- Create subject_trade_combinations table if it doesn't exist
CREATE TABLE IF NOT EXISTS subject_trade_combinations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  trade_id INT NOT NULL,
  subject_id INT NOT NULL,
  is_compulsory BOOLEAN DEFAULT TRUE,
  is_elective BOOLEAN DEFAULT FALSE,
  is_additional BOOLEAN DEFAULT FALSE,
  is_optional BOOLEAN DEFAULT FALSE,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Added academic session column
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
  UNIQUE KEY (trade_id, subject_id, academic_session)
);

-- Create exams table if it doesn't exist
CREATE TABLE IF NOT EXISTS exams (
  exam_id INT AUTO_INCREMENT PRIMARY KEY,
  exam_name VARCHAR(255) NOT NULL,
  description TEXT,
  duration INT NOT NULL,
  total_questions INT NOT NULL DEFAULT 0,
  passing_marks DECIMAL(5,2) DEFAULT 40.00,
  category_id INT,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Added academic session column
  is_active BOOLEAN DEFAULT TRUE,
  is_deleted BOOLEAN DEFAULT FALSE,
  is_resumable BOOLEAN DEFAULT FALSE,
  created_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create exam_attempts table if it doesn't exist
CREATE TABLE IF NOT EXISTS exam_attempts (
  attempt_id INT AUTO_INCREMENT PRIMARY KEY,
  exam_id INT NOT NULL,
  user_id INT NOT NULL,
  status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'in_progress',
  start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  end_time DATETIME,
  total_score DECIMAL(5,2),
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Added academic session column
  continuation_count INT DEFAULT 0,
  attempt_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (exam_id) REFERENCES exams(exam_id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create exam_subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS exam_subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  exam_id INT NOT NULL,
  subject_id INT NOT NULL,
  max_marks DECIMAL(5,2) NOT NULL DEFAULT 100.00,
  passing_marks DECIMAL(5,2) NOT NULL DEFAULT 33.00,
  is_compulsory BOOLEAN DEFAULT TRUE,
  is_elective BOOLEAN DEFAULT FALSE,
  is_additional BOOLEAN DEFAULT FALSE,
  is_optional BOOLEAN DEFAULT FALSE,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Added academic session column
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (exam_id) REFERENCES exams(exam_id) ON DELETE CASCADE,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
  UNIQUE KEY (exam_id, subject_id, academic_session)
);

-- Create student_subject_marks table if it doesn't exist
CREATE TABLE IF NOT EXISTS student_subject_marks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_id INT NOT NULL,
  exam_id INT NOT NULL,
  subject_id INT NOT NULL,
  theory_marks DECIMAL(5,2) DEFAULT 0,
  practical_marks DECIMAL(5,2) DEFAULT 0,
  internal_marks DECIMAL(5,2) DEFAULT 0,
  total_marks DECIMAL(5,2) DEFAULT 0,
  max_marks DECIMAL(5,2) DEFAULT 100.00,
  percentage DECIMAL(5,2) DEFAULT 0,
  grade VARCHAR(5),
  is_pass BOOLEAN DEFAULT FALSE,
  remarks TEXT,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Academic session column
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
  FOREIGN KEY (exam_id) REFERENCES exams(exam_id) ON DELETE CASCADE,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
  UNIQUE KEY (student_id, exam_id, subject_id, academic_session)
);

-- Create classrooms table if it doesn't exist
CREATE TABLE IF NOT EXISTS classrooms (
  id INT AUTO_INCREMENT PRIMARY KEY,
  class_id INT NOT NULL,
  section_id INT NOT NULL,
  trade_id INT,
  room_id INT,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Academic session column
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
  FOREIGN KEY (section_id) REFERENCES sections(id) ON DELETE CASCADE,
  FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE SET NULL,
  UNIQUE KEY (class_id, section_id, trade_id, academic_session)
);

-- Create teacher_classes table if it doesn't exist
CREATE TABLE IF NOT EXISTS teacher_classes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  teacher_id INT NOT NULL,
  classroom_id INT NOT NULL,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Added academic session column
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE,
  UNIQUE KEY (teacher_id, classroom_id, academic_session)
);

-- Create teacher_subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS teacher_subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  teacher_id INT NOT NULL,
  subject_id INT NOT NULL,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Added academic session column
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
  UNIQUE KEY (teacher_id, subject_id, academic_session)
);

-- Create class_incharge table if it doesn't exist
CREATE TABLE IF NOT EXISTS class_incharge (
  id INT AUTO_INCREMENT PRIMARY KEY,
  class_id INT NOT NULL,
  teacher_id INT NOT NULL,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024', -- Academic session column
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY (class_id, academic_session)
);

-- Create a view for student report cards
CREATE OR REPLACE VIEW student_report_cards AS
SELECT
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.father_name,
    s.mother_name,
    c.name AS class_name,
    sec.name AS section_name,
    s.academic_session,
    t.name AS trade_name,
    str.name AS stream_name,
    e.exam_id,
    e.exam_name,
    sub.id AS subject_id,
    sub.name AS subject_name,
    ssm.theory_marks,
    ssm.practical_marks,
    ssm.internal_marks,
    ssm.total_marks,
    ssm.max_marks,
    ssm.percentage,
    ssm.grade,
    ssm.is_pass,
    CASE
        WHEN ss.is_compulsory = TRUE THEN 'Compulsory'
        WHEN ss.is_elective = TRUE THEN 'Elective'
        WHEN ss.is_additional = TRUE THEN 'Additional'
        WHEN ss.is_optional = TRUE THEN 'Optional'
        ELSE 'Regular'
    END AS subject_type
FROM
    students s
    JOIN classes c ON s.class_id = c.id
    JOIN sections sec ON s.section_id = sec.id
    LEFT JOIN trades t ON s.trade_id = t.id
    LEFT JOIN streams str ON s.stream_id = str.id
    JOIN student_subjects ss ON s.id = ss.student_id AND ss.academic_session = s.academic_session
    JOIN subjects sub ON ss.subject_id = sub.id
    JOIN student_subject_marks ssm ON s.id = ssm.student_id
        AND sub.id = ssm.subject_id
        AND ssm.academic_session = s.academic_session
    JOIN exams e ON ssm.exam_id = e.exam_id AND e.academic_session = s.academic_session;

-- Create a view for class-wise student performance
CREATE OR REPLACE VIEW class_performance AS
SELECT
    c.id AS class_id,
    c.name AS class_name,
    sec.name AS section_name,
    t.name AS trade_name,
    e.exam_id,
    e.exam_name,
    sub.id AS subject_id,
    sub.name AS subject_name,
    s.academic_session,
    COUNT(DISTINCT s.id) AS total_students,
    SUM(CASE WHEN ssm.is_pass = TRUE THEN 1 ELSE 0 END) AS passed_students,
    ROUND(AVG(ssm.percentage), 2) AS average_percentage,
    MAX(ssm.percentage) AS highest_percentage,
    MIN(ssm.percentage) AS lowest_percentage
FROM
    students s
    JOIN classes c ON s.class_id = c.id
    JOIN sections sec ON s.section_id = sec.id
    LEFT JOIN trades t ON s.trade_id = t.id
    JOIN student_subjects ss ON s.id = ss.student_id AND ss.academic_session = s.academic_session
    JOIN subjects sub ON ss.subject_id = sub.id
    JOIN student_subject_marks ssm ON s.id = ssm.student_id
        AND sub.id = ssm.subject_id
        AND ssm.academic_session = s.academic_session
    JOIN exams e ON ssm.exam_id = e.exam_id AND e.academic_session = s.academic_session
GROUP BY
    c.id, sec.id, t.id, e.exam_id, sub.id, s.academic_session;

-- Migrate data from string columns to foreign key columns
-- This will update the trade_id, class_id, section_id, and stream_id columns
-- based on the existing string values in the trade, class, section, and stream columns

-- Update trade_id based on trade name
UPDATE students s
JOIN trades t ON s.trade = t.name
SET s.trade_id = t.id
WHERE s.trade_id IS NULL AND s.trade IS NOT NULL;

-- Update class_id based on class name
UPDATE students s
JOIN classes c ON s.class = c.grade
SET s.class_id = c.id
WHERE s.class_id IS NULL AND s.class IS NOT NULL;

-- Update section_id based on section name
UPDATE students s
JOIN sections sec ON s.section = sec.name
SET s.section_id = sec.id
WHERE s.section_id IS NULL AND s.section IS NOT NULL;

-- Update stream_id based on stream name
UPDATE students s
JOIN streams str ON s.stream = str.name
SET s.stream_id = str.id
WHERE s.stream_id IS NULL AND s.stream IS NOT NULL;

-- If students table has a 'session' column, copy its values to 'academic_session'
SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'students'
  AND COLUMN_NAME = 'session'
);

SET @sql = IF(@column_exists > 0,
  'UPDATE students SET academic_session = session WHERE academic_session IS NULL AND session IS NOT NULL',
  'SELECT "No session column to migrate" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add academic_session indexes to improve query performance
ALTER TABLE students ADD INDEX idx_student_academic_session (academic_session);
ALTER TABLE classes ADD INDEX idx_class_academic_session (academic_session);
ALTER TABLE student_classes ADD INDEX idx_student_class_academic_session (academic_session);
ALTER TABLE student_subjects ADD INDEX idx_student_subject_academic_session (academic_session);
ALTER TABLE subject_trade_combinations ADD INDEX idx_subject_trade_academic_session (academic_session);
ALTER TABLE exams ADD INDEX idx_exam_academic_session (academic_session);
ALTER TABLE exam_attempts ADD INDEX idx_exam_attempt_academic_session (academic_session);
ALTER TABLE exam_subjects ADD INDEX idx_exam_subject_academic_session (academic_session);
ALTER TABLE student_subject_marks ADD INDEX idx_student_marks_academic_session (academic_session);
ALTER TABLE classrooms ADD INDEX idx_classroom_academic_session (academic_session);
ALTER TABLE teacher_classes ADD INDEX idx_teacher_class_academic_session (academic_session);
ALTER TABLE teacher_subjects ADD INDEX idx_teacher_subject_academic_session (academic_session);
ALTER TABLE class_incharge ADD INDEX idx_class_incharge_academic_session (academic_session);

-- Commit the transaction
COMMIT;

-- Show a success message
SELECT 'Database structure updated successfully with academic session fields' AS message;
