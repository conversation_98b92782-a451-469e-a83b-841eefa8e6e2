-- Fixed script to update database structure with academic session columns
-- This script works with the existing database structure
-- It will:
-- 1. Create missing tables if they don't exist (avoiding conflicts)
-- 2. Add missing columns to existing tables
-- 3. Create tables for subject categorization and exam marks
-- 4. Add academic session columns to all relevant tables

-- Start transaction
START TRANSACTION;

-- Create class_sections table (separate from existing sections table which is for exams)
CREATE TABLE IF NOT EXISTS class_sections (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(10) NOT NULL,
  display_name VARCHAR(50),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHA<PERSON>(100) NOT NULL,
  code VARCHAR(20) UNIQUE,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add missing columns to existing tables

-- Check if section_id column exists in students table
SET @column_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'students' 
  AND COLUMN_NAME = 'section_id'
);

-- Add section_id if it doesn't exist (referencing class_sections, not sections)
SET @sql = IF(@column_exists = 0, 
  'ALTER TABLE students ADD COLUMN section_id INT',
  'SELECT "section_id column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key for section_id after creating the column
SET @fk_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
  WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'students' 
  AND CONSTRAINT_NAME = 'fk_student_section'
);

SET @sql = IF(@fk_exists = 0 AND @column_exists = 0, 
  'ALTER TABLE students ADD CONSTRAINT fk_student_section FOREIGN KEY (section_id) REFERENCES class_sections(id)',
  'SELECT "section_id foreign key already exists or column was not created" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add academic_session column to classes table if it doesn't exist
SET @column_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'classes' 
  AND COLUMN_NAME = 'academic_session'
);

SET @sql = IF(@column_exists = 0, 
  'ALTER TABLE classes ADD COLUMN academic_session VARCHAR(20) DEFAULT "2023-2024"',
  'SELECT "academic_session column already exists in classes table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create student_classes junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS student_classes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_id INT NOT NULL,
  class_id INT NOT NULL,
  section_id INT,
  stream_id INT,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024',
  is_current BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
  FOREIGN KEY (section_id) REFERENCES class_sections(id) ON DELETE CASCADE,
  FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE CASCADE,
  UNIQUE KEY (student_id, class_id, section_id, academic_session)
);

-- Create student_subjects junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS student_subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_id INT NOT NULL,
  subject_id INT NOT NULL,
  trade_id INT,
  is_compulsory BOOLEAN DEFAULT TRUE,
  is_elective BOOLEAN DEFAULT FALSE,
  is_additional BOOLEAN DEFAULT FALSE,
  is_optional BOOLEAN DEFAULT FALSE,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
  FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE SET NULL,
  UNIQUE KEY (student_id, subject_id, academic_session)
);

-- Create subject_trade_combinations table if it doesn't exist
CREATE TABLE IF NOT EXISTS subject_trade_combinations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  trade_id INT NOT NULL,
  subject_id INT NOT NULL,
  is_compulsory BOOLEAN DEFAULT TRUE,
  is_elective BOOLEAN DEFAULT FALSE,
  is_additional BOOLEAN DEFAULT FALSE,
  is_optional BOOLEAN DEFAULT FALSE,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
  UNIQUE KEY (trade_id, subject_id, academic_session)
);

-- Add academic_session column to exams table if it doesn't exist
SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'exams'
  AND COLUMN_NAME = 'academic_session'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE exams ADD COLUMN academic_session VARCHAR(20) DEFAULT "2023-2024"',
  'SELECT "academic_session column already exists in exams table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add academic_session column to exam_attempts table if it doesn't exist
SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'exam_attempts'
  AND COLUMN_NAME = 'academic_session'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE exam_attempts ADD COLUMN academic_session VARCHAR(20) DEFAULT "2023-2024"',
  'SELECT "academic_session column already exists in exam_attempts table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add academic_session column to exam_subjects table if it doesn't exist
SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'exam_subjects'
  AND COLUMN_NAME = 'academic_session'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE exam_subjects ADD COLUMN academic_session VARCHAR(20) DEFAULT "2023-2024"',
  'SELECT "academic_session column already exists in exam_subjects table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add academic_session column to student_subject_marks table if it doesn't exist
SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'student_subject_marks'
  AND COLUMN_NAME = 'academic_session'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE student_subject_marks ADD COLUMN academic_session VARCHAR(20) DEFAULT "2023-2024"',
  'SELECT "academic_session column already exists in student_subject_marks table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add missing columns to student_subject_marks table for comprehensive marking
SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'student_subject_marks'
  AND COLUMN_NAME = 'theory_marks'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE student_subject_marks ADD COLUMN theory_marks DECIMAL(5,2) DEFAULT 0',
  'SELECT "theory_marks column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'student_subject_marks'
  AND COLUMN_NAME = 'practical_marks'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE student_subject_marks ADD COLUMN practical_marks DECIMAL(5,2) DEFAULT 0',
  'SELECT "practical_marks column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'student_subject_marks'
  AND COLUMN_NAME = 'internal_marks'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE student_subject_marks ADD COLUMN internal_marks DECIMAL(5,2) DEFAULT 0',
  'SELECT "internal_marks column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'student_subject_marks'
  AND COLUMN_NAME = 'percentage'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE student_subject_marks ADD COLUMN percentage DECIMAL(5,2) DEFAULT 0',
  'SELECT "percentage column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'student_subject_marks'
  AND COLUMN_NAME = 'grade'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE student_subject_marks ADD COLUMN grade VARCHAR(5)',
  'SELECT "grade column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'student_subject_marks'
  AND COLUMN_NAME = 'is_pass'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE student_subject_marks ADD COLUMN is_pass BOOLEAN DEFAULT FALSE',
  'SELECT "is_pass column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add academic_session columns to other tables
SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'teacher_classes'
  AND COLUMN_NAME = 'academic_session'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE teacher_classes ADD COLUMN academic_session VARCHAR(20) DEFAULT "2023-2024"',
  'SELECT "academic_session column already exists in teacher_classes table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'teacher_subjects'
  AND COLUMN_NAME = 'academic_session'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE teacher_subjects ADD COLUMN academic_session VARCHAR(20) DEFAULT "2023-2024"',
  'SELECT "academic_session column already exists in teacher_subjects table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'class_incharge'
  AND COLUMN_NAME = 'academic_session'
);

SET @sql = IF(@column_exists = 0,
  'ALTER TABLE class_incharge ADD COLUMN academic_session VARCHAR(20) DEFAULT "2023-2024"',
  'SELECT "academic_session column already exists in class_incharge table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Migrate data from string columns to foreign key columns
-- Update section_id based on section name (using class_sections table)
UPDATE students s
JOIN class_sections cs ON s.section = cs.name
SET s.section_id = cs.id
WHERE s.section_id IS NULL AND s.section IS NOT NULL;

-- If students table has a 'session' column, copy its values to 'academic_session'
SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'students'
  AND COLUMN_NAME = 'session'
);

SET @sql = IF(@column_exists > 0,
  'UPDATE students SET academic_session = session WHERE academic_session = "2023-2024" AND session IS NOT NULL',
  'SELECT "No session column to migrate" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Copy session data from classes table if it exists
SET @column_exists = (
  SELECT COUNT(*)
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'classes'
  AND COLUMN_NAME = 'session'
);

SET @sql = IF(@column_exists > 0,
  'UPDATE classes SET academic_session = session WHERE academic_session = "2023-2024" AND session IS NOT NULL',
  'SELECT "No session column to migrate in classes table" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Insert some default class sections if the table is empty
INSERT IGNORE INTO class_sections (name, display_name) VALUES
('A', 'Section A'),
('B', 'Section B'),
('C', 'Section C'),
('D', 'Section D');

-- Insert some default subjects if the table is empty
INSERT IGNORE INTO subjects (name, code, description) VALUES
('Mathematics', 'MATH', 'Mathematics subject'),
('English', 'ENG', 'English subject'),
('Science', 'SCI', 'Science subject'),
('Social Studies', 'SS', 'Social Studies subject'),
('Computer Science', 'CS', 'Computer Science subject');

-- Commit the transaction
COMMIT;

-- Show a success message
SELECT 'Database structure updated successfully with academic session fields' AS message;
