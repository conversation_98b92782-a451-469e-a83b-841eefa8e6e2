-- =====================================================
-- SAMPLE DATA FOR INSIGHTS QUERIES TESTING
-- This script creates properly linked sample data for testing the insights queries
-- =====================================================

-- Clear existing data (optional - uncomment if needed)
-- DELETE FROM assignment_submissions;
-- DELETE FROM assignments;
-- DELETE FROM practical_records;
-- DELETE FROM practicals;
-- DELETE FROM exam_attempts;
-- DELETE FROM student_practical_records;
-- DELETE FROM teacher_classes;
-- DELETE FROM teacher_subjects;
-- DELETE FROM class_incharge;
-- DELETE FROM student_subjects;
-- DELETE FROM student_classes;
-- DELETE FROM classrooms;
-- DELETE FROM students WHERE student_id LIKE 'SAMP%';
-- DELETE FROM users WHERE username LIKE 'teacher%' OR username LIKE 'student%';
-- DELETE FROM subjects WHERE code LIKE 'SAMP%';
-- DELETE FROM classes WHERE name LIKE 'Sample%';
-- DELETE FROM trades WHERE name LIKE 'Sample%';
-- DELETE FROM rooms WHERE room_number LIKE 'SR%';

-- =====================================================
-- 1. BASIC STRUCTURE DATA
-- =====================================================

-- Insert sample rooms
INSERT INTO rooms (room_number, capacity, building, floor) VALUES
('SR101', 50, 'Main Building', 1),
('SR102', 50, 'Main Building', 1),
('SR201', 50, 'Main Building', 2),
('SR202', 50, 'Main Building', 2),
('SR301', 50, 'Science Block', 3),
('SL101', 30, 'Lab Block', 1),
('SL102', 30, 'Lab Block', 1)
ON DUPLICATE KEY UPDATE capacity = VALUES(capacity);

-- Insert sample trades
INSERT INTO trades (name, description) VALUES
('Science', 'Science stream with PCM/PCB'),
('Commerce', 'Commerce stream with accounts'),
('Arts', 'Arts stream with humanities'),
('Computer Science', 'Computer Science specialization')
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- Insert sample classes
INSERT INTO classes (name, grade, description, is_active) VALUES
('Sample Class 10', '10', 'Sample 10th grade class', 1),
('Sample Class 11', '11', 'Sample 11th grade class', 1),
('Sample Class 12', '12', 'Sample 12th grade class', 1)
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- Insert sample subjects
INSERT INTO subjects (name, code, description, subject_group) VALUES
('Mathematics', 'SAMPMATH', 'Sample Mathematics subject', 10),
('Physics', 'SAMPPHY', 'Sample Physics subject', 11),
('Chemistry', 'SAMPCHEM', 'Sample Chemistry subject', 11),
('Computer Science', 'SAMPCS', 'Sample Computer Science subject', 12),
('English', 'SAMPENG', 'Sample English subject', 10),
('Biology', 'SAMPBIO', 'Sample Biology subject', 11)
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- =====================================================
-- 2. USERS (TEACHERS AND STUDENTS)
-- =====================================================

-- Insert sample teachers
INSERT INTO users (username, name, email, password, role, subjects, bio, date_of_birth, gender, is_active) VALUES
('teacher_math', 'Dr. Rajesh Kumar', '<EMAIL>', '$2b$10$sample_hash', 'teacher', 'Mathematics', 'Mathematics teacher with 10 years experience', '1985-05-15', 'male', 1),
('teacher_physics', 'Prof. Sunita Sharma', '<EMAIL>', '$2b$10$sample_hash', 'teacher', 'Physics', 'Physics teacher and lab coordinator', '1982-08-22', 'female', 1),
('teacher_chemistry', 'Dr. Amit Singh', '<EMAIL>', '$2b$10$sample_hash', 'teacher', 'Chemistry', 'Chemistry teacher with research background', '1980-12-10', 'male', 1),
('teacher_cs', 'Ms. Priya Patel', '<EMAIL>', '$2b$10$sample_hash', 'teacher', 'Computer Science', 'Computer Science teacher and programmer', '1988-03-18', 'female', 1),
('teacher_english', 'Mr. Vikram Joshi', '<EMAIL>', '$2b$10$sample_hash', 'teacher', 'English', 'English literature teacher', '1983-07-25', 'male', 1),
('teacher_biology', 'Dr. Meera Gupta', '<EMAIL>', '$2b$10$sample_hash', 'teacher', 'Biology', 'Biology teacher and nature enthusiast', '1984-11-30', 'female', 1)
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- Insert sample students
INSERT INTO students (
    sno, student_id, name, father_name, mother_name, dob, gender, 
    class, section, session, trade, room_number, is_active
) VALUES
(1, 'SAMP001', 'Rahul Sharma', 'Suresh Sharma', 'Kavita Sharma', '2008-01-15', 'Male', '10', 'A', '2024-25', 'Science', 'SR101', 1),
(2, 'SAMP002', 'Priya Singh', 'Rajesh Singh', 'Sunita Singh', '2008-03-22', 'Female', '10', 'A', '2024-25', 'Science', 'SR101', 1),
(3, 'SAMP003', 'Amit Kumar', 'Vinod Kumar', 'Rekha Kumar', '2008-05-10', 'Male', '10', 'A', '2024-25', 'Science', 'SR101', 1),
(4, 'SAMP004', 'Sneha Patel', 'Mahesh Patel', 'Nisha Patel', '2008-07-18', 'Female', '10', 'B', '2024-25', 'Commerce', 'SR102', 1),
(5, 'SAMP005', 'Vikash Gupta', 'Ramesh Gupta', 'Pooja Gupta', '2008-09-25', 'Male', '10', 'B', '2024-25', 'Commerce', 'SR102', 1),
(6, 'SAMP006', 'Anjali Joshi', 'Prakash Joshi', 'Meera Joshi', '2007-02-12', 'Female', '11', 'A', '2024-25', 'Science', 'SR201', 1),
(7, 'SAMP007', 'Rohit Verma', 'Sunil Verma', 'Asha Verma', '2007-04-20', 'Male', '11', 'A', '2024-25', 'Science', 'SR201', 1),
(8, 'SAMP008', 'Kavya Agarwal', 'Deepak Agarwal', 'Sita Agarwal', '2007-06-28', 'Female', '11', 'B', '2024-25', 'Commerce', 'SR202', 1),
(9, 'SAMP009', 'Arjun Yadav', 'Mohan Yadav', 'Radha Yadav', '2006-01-08', 'Male', '12', 'A', '2024-25', 'Science', 'SR301', 1),
(10, 'SAMP010', 'Riya Malhotra', 'Ajay Malhotra', 'Neha Malhotra', '2006-03-16', 'Female', '12', 'A', '2024-25', 'Science', 'SR301', 1)
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- Create corresponding user accounts for students
INSERT INTO users (username, name, email, password, role, date_of_birth, gender, is_active)
SELECT 
    LOWER(CONCAT('student_', s.student_id)),
    s.name,
    LOWER(CONCAT(REPLACE(s.name, ' ', '.'), '@student.school.edu')),
    '$2b$10$sample_hash',
    'student',
    s.dob,
    LOWER(s.gender),
    1
FROM students s 
WHERE s.student_id LIKE 'SAMP%'
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- =====================================================
-- 3. CLASSROOM AND ASSIGNMENT SETUP
-- =====================================================

-- Get trade IDs
SET @science_trade_id = (SELECT id FROM trades WHERE name = 'Science' LIMIT 1);
SET @commerce_trade_id = (SELECT id FROM trades WHERE name = 'Commerce' LIMIT 1);
SET @class_10_id = (SELECT id FROM classes WHERE grade = '10' LIMIT 1);
SET @class_11_id = (SELECT id FROM classes WHERE grade = '11' LIMIT 1);
SET @class_12_id = (SELECT id FROM classes WHERE grade = '12' LIMIT 1);

-- Insert classrooms
INSERT INTO classrooms (session, room_id, class_id, trade_id, section, is_active) VALUES
('2024-25', (SELECT id FROM rooms WHERE room_number = 'SR101'), @class_10_id, @science_trade_id, 'A', 1),
('2024-25', (SELECT id FROM rooms WHERE room_number = 'SR102'), @class_10_id, @commerce_trade_id, 'B', 1),
('2024-25', (SELECT id FROM rooms WHERE room_number = 'SR201'), @class_11_id, @science_trade_id, 'A', 1),
('2024-25', (SELECT id FROM rooms WHERE room_number = 'SR202'), @class_11_id, @commerce_trade_id, 'B', 1),
('2024-25', (SELECT id FROM rooms WHERE room_number = 'SR301'), @class_12_id, @science_trade_id, 'A', 1)
ON DUPLICATE KEY UPDATE is_active = VALUES(is_active);

-- =====================================================
-- 4. TEACHER-SUBJECT-CLASS ASSIGNMENTS
-- =====================================================

-- Get teacher IDs
SET @teacher_math_id = (SELECT id FROM users WHERE username = 'teacher_math');
SET @teacher_physics_id = (SELECT id FROM users WHERE username = 'teacher_physics');
SET @teacher_chemistry_id = (SELECT id FROM users WHERE username = 'teacher_chemistry');
SET @teacher_cs_id = (SELECT id FROM users WHERE username = 'teacher_cs');
SET @teacher_english_id = (SELECT id FROM users WHERE username = 'teacher_english');
SET @teacher_biology_id = (SELECT id FROM users WHERE username = 'teacher_biology');

-- Get subject IDs
SET @math_subject_id = (SELECT id FROM subjects WHERE code = 'SAMPMATH');
SET @physics_subject_id = (SELECT id FROM subjects WHERE code = 'SAMPPHY');
SET @chemistry_subject_id = (SELECT id FROM subjects WHERE code = 'SAMPCHEM');
SET @cs_subject_id = (SELECT id FROM subjects WHERE code = 'SAMPCS');
SET @english_subject_id = (SELECT id FROM subjects WHERE code = 'SAMPENG');
SET @biology_subject_id = (SELECT id FROM subjects WHERE code = 'SAMPBIO');

-- Assign teachers to subjects
INSERT INTO teacher_subjects (teacher_id, subject_id) VALUES
(@teacher_math_id, @math_subject_id),
(@teacher_physics_id, @physics_subject_id),
(@teacher_chemistry_id, @chemistry_subject_id),
(@teacher_cs_id, @cs_subject_id),
(@teacher_english_id, @english_subject_id),
(@teacher_biology_id, @biology_subject_id)
ON DUPLICATE KEY UPDATE teacher_id = VALUES(teacher_id);

-- Assign teachers to classrooms
INSERT INTO teacher_classes (teacher_id, classroom_id)
SELECT 
    @teacher_math_id,
    cr.id
FROM classrooms cr 
WHERE cr.session = '2024-25'
ON DUPLICATE KEY UPDATE teacher_id = VALUES(teacher_id);

-- Additional teacher-classroom assignments
INSERT INTO teacher_classes (teacher_id, classroom_id)
SELECT 
    CASE 
        WHEN cr.trade_id = @science_trade_id THEN @teacher_physics_id
        WHEN cr.trade_id = @commerce_trade_id THEN @teacher_english_id
        ELSE @teacher_math_id
    END,
    cr.id
FROM classrooms cr 
WHERE cr.session = '2024-25'
ON DUPLICATE KEY UPDATE teacher_id = VALUES(teacher_id);

-- Set class incharges
INSERT INTO class_incharge (class_id, teacher_id) VALUES
(@class_10_id, @teacher_math_id),
(@class_11_id, @teacher_physics_id),
(@class_12_id, @teacher_chemistry_id)
ON DUPLICATE KEY UPDATE teacher_id = VALUES(teacher_id);

-- =====================================================
-- 5. STUDENT-SUBJECT ASSIGNMENTS
-- =====================================================

-- Assign subjects to students based on their trade
INSERT INTO student_subjects (student_id, subject_id)
SELECT 
    u.id,
    sub.id
FROM users u
JOIN students s ON LOWER(CONCAT('student_', s.student_id)) = u.username
CROSS JOIN subjects sub
WHERE u.role = 'student' 
    AND s.student_id LIKE 'SAMP%'
    AND (
        (s.trade = 'Science' AND sub.code IN ('SAMPMATH', 'SAMPPHY', 'SAMPCHEM', 'SAMPENG', 'SAMPCS')) OR
        (s.trade = 'Commerce' AND sub.code IN ('SAMPMATH', 'SAMPENG', 'SAMPCS'))
    )
ON DUPLICATE KEY UPDATE student_id = VALUES(student_id);

-- =====================================================
-- 6. SAMPLE ACADEMIC DATA (EXAMS, PRACTICALS, ASSIGNMENTS)
-- =====================================================

-- Create sample exams
INSERT INTO exams (exam_name, description, duration, passing_marks, status, difficulty, created_by) VALUES
('Mathematics Unit Test 1', 'First unit test for Mathematics', 60, 40.00, 'published', 'intermediate', @teacher_math_id),
('Physics Mid-term', 'Mid-term examination for Physics', 90, 40.00, 'published', 'intermediate', @teacher_physics_id),
('Chemistry Practical Test', 'Practical test for Chemistry', 120, 40.00, 'published', 'intermediate', @teacher_chemistry_id),
('Computer Science Quiz', 'Programming concepts quiz', 45, 40.00, 'published', 'beginner', @teacher_cs_id),
('English Literature Test', 'Test on poetry and prose', 75, 40.00, 'published', 'intermediate', @teacher_english_id)
ON DUPLICATE KEY UPDATE exam_name = VALUES(exam_name);

-- Create sample exam attempts with realistic scores
INSERT INTO exam_attempts (user_id, exam_id, score, total_questions, status, start_time, end_time, attempt_date)
SELECT
    u.id,
    e.exam_id,
    CASE
        WHEN u.username LIKE '%001' OR u.username LIKE '%002' THEN ROUND(75 + (RAND() * 20), 2)  -- Top performers
        WHEN u.username LIKE '%003' OR u.username LIKE '%004' THEN ROUND(65 + (RAND() * 20), 2)  -- Good performers
        WHEN u.username LIKE '%005' OR u.username LIKE '%006' THEN ROUND(55 + (RAND() * 20), 2)  -- Average performers
        ELSE ROUND(45 + (RAND() * 25), 2)  -- Mixed performers
    END as score,
    CASE
        WHEN e.exam_name LIKE '%Quiz%' THEN 20
        WHEN e.exam_name LIKE '%Unit Test%' THEN 30
        WHEN e.exam_name LIKE '%Mid-term%' THEN 50
        ELSE 40
    END as total_questions,
    'completed',
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY),
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY) + INTERVAL e.duration MINUTE,
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)
FROM users u
CROSS JOIN exams e
WHERE u.role = 'student'
    AND u.username LIKE 'student_samp%'
    AND e.exam_name LIKE '%Test%' OR e.exam_name LIKE '%Quiz%' OR e.exam_name LIKE '%Mid-term%'
ON DUPLICATE KEY UPDATE score = VALUES(score);

-- Create sample practicals
INSERT INTO practicals (class_id, subject_id, teacher_id, description, date, start_time, end_time, location, requirements)
VALUES
(@class_10_id, @physics_subject_id, @teacher_physics_id, 'Ohms Law Experiment', DATE_SUB(NOW(), INTERVAL 15 DAY), '09:00:00', '11:00:00', 'Physics Lab', 'Multimeter, Resistors, Wires'),
(@class_10_id, @chemistry_subject_id, @teacher_chemistry_id, 'Acid-Base Titration', DATE_SUB(NOW(), INTERVAL 12 DAY), '10:00:00', '12:00:00', 'Chemistry Lab', 'Burette, Pipette, Indicators'),
(@class_11_id, @physics_subject_id, @teacher_physics_id, 'Pendulum Experiment', DATE_SUB(NOW(), INTERVAL 10 DAY), '09:00:00', '11:00:00', 'Physics Lab', 'Pendulum setup, Stopwatch'),
(@class_11_id, @cs_subject_id, @teacher_cs_id, 'Basic Programming', DATE_SUB(NOW(), INTERVAL 8 DAY), '11:00:00', '13:00:00', 'Computer Lab', 'Computer, IDE'),
(@class_12_id, @chemistry_subject_id, @teacher_chemistry_id, 'Organic Synthesis', DATE_SUB(NOW(), INTERVAL 5 DAY), '10:00:00', '13:00:00', 'Chemistry Lab', 'Organic compounds, Glassware')
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- Create sample practical records with grades
INSERT INTO practical_records (practical_id, student_id, submission_text, status, grade, feedback, reviewed_by, reviewed_at)
SELECT
    p.id,
    u.id,
    CONCAT('Practical submission for ', p.description, ' by ', u.name),
    'graded',
    CASE
        WHEN u.username LIKE '%001' OR u.username LIKE '%002' THEN 'A+'
        WHEN u.username LIKE '%003' OR u.username LIKE '%004' THEN 'A'
        WHEN u.username LIKE '%005' OR u.username LIKE '%006' THEN 'B+'
        WHEN u.username LIKE '%007' OR u.username LIKE '%008' THEN 'B'
        ELSE 'C+'
    END as grade,
    CASE
        WHEN u.username LIKE '%001' OR u.username LIKE '%002' THEN 'Excellent work with clear understanding'
        WHEN u.username LIKE '%003' OR u.username LIKE '%004' THEN 'Good work, minor improvements needed'
        WHEN u.username LIKE '%005' OR u.username LIKE '%006' THEN 'Satisfactory work, practice more'
        ELSE 'Needs improvement in methodology'
    END as feedback,
    CASE
        WHEN p.subject_id = @physics_subject_id THEN @teacher_physics_id
        WHEN p.subject_id = @chemistry_subject_id THEN @teacher_chemistry_id
        WHEN p.subject_id = @cs_subject_id THEN @teacher_cs_id
        ELSE @teacher_math_id
    END,
    DATE_ADD(p.date, INTERVAL 3 DAY)
FROM practicals p
CROSS JOIN users u
JOIN students s ON LOWER(CONCAT('student_', s.student_id)) = u.username
JOIN classes c ON s.class = c.grade
WHERE u.role = 'student'
    AND u.username LIKE 'student_samp%'
    AND p.class_id = c.id
ON DUPLICATE KEY UPDATE grade = VALUES(grade);

-- Create sample assignments
INSERT INTO assignments (title, description, subject_id, class_id, teacher_id, due_date, total_marks)
VALUES
('Algebra Problem Set', 'Solve quadratic equations and inequalities', @math_subject_id, @class_10_id, @teacher_math_id, DATE_ADD(NOW(), INTERVAL 7 DAY), 25),
('Physics Numerical Problems', 'Solve problems on motion and force', @physics_subject_id, @class_11_id, @teacher_physics_id, DATE_ADD(NOW(), INTERVAL 10 DAY), 30),
('Chemistry Lab Report', 'Write report on recent titration experiment', @chemistry_subject_id, @class_10_id, @teacher_chemistry_id, DATE_ADD(NOW(), INTERVAL 5 DAY), 20),
('Programming Assignment', 'Create a simple calculator program', @cs_subject_id, @class_11_id, @teacher_cs_id, DATE_ADD(NOW(), INTERVAL 14 DAY), 35),
('English Essay', 'Write essay on environmental conservation', @english_subject_id, @class_10_id, @teacher_english_id, DATE_ADD(NOW(), INTERVAL 12 DAY), 25),
('Mathematics Project', 'Statistics project on school data', @math_subject_id, @class_12_id, @teacher_math_id, DATE_ADD(NOW(), INTERVAL 21 DAY), 40)
ON DUPLICATE KEY UPDATE title = VALUES(title);

-- Create sample assignment submissions with marks
INSERT INTO assignment_submissions (assignment_id, student_id, submission_text, status, marks_obtained, feedback, graded_by, graded_at)
SELECT
    a.id,
    u.id,
    CONCAT('Assignment submission for "', a.title, '" by ', u.name),
    'graded',
    CASE
        WHEN u.username LIKE '%001' OR u.username LIKE '%002' THEN ROUND(a.total_marks * 0.9 + (RAND() * a.total_marks * 0.1), 0)  -- 90-100%
        WHEN u.username LIKE '%003' OR u.username LIKE '%004' THEN ROUND(a.total_marks * 0.8 + (RAND() * a.total_marks * 0.15), 0) -- 80-95%
        WHEN u.username LIKE '%005' OR u.username LIKE '%006' THEN ROUND(a.total_marks * 0.7 + (RAND() * a.total_marks * 0.2), 0)  -- 70-90%
        WHEN u.username LIKE '%007' OR u.username LIKE '%008' THEN ROUND(a.total_marks * 0.6 + (RAND() * a.total_marks * 0.25), 0) -- 60-85%
        ELSE ROUND(a.total_marks * 0.5 + (RAND() * a.total_marks * 0.3), 0)  -- 50-80%
    END as marks_obtained,
    CASE
        WHEN u.username LIKE '%001' OR u.username LIKE '%002' THEN 'Outstanding work! Keep it up.'
        WHEN u.username LIKE '%003' OR u.username LIKE '%004' THEN 'Very good work with minor areas for improvement.'
        WHEN u.username LIKE '%005' OR u.username LIKE '%006' THEN 'Good effort, but needs more attention to detail.'
        WHEN u.username LIKE '%007' OR u.username LIKE '%008' THEN 'Satisfactory work, practice more for better results.'
        ELSE 'Needs significant improvement. Please see me for help.'
    END as feedback,
    a.teacher_id,
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 10) DAY)
FROM assignments a
CROSS JOIN users u
JOIN students s ON LOWER(CONCAT('student_', s.student_id)) = u.username
JOIN classes c ON s.class = c.grade
WHERE u.role = 'student'
    AND u.username LIKE 'student_samp%'
    AND (a.class_id = c.id OR a.class_id IS NULL)
    AND a.due_date > DATE_SUB(NOW(), INTERVAL 15 DAY)  -- Only recent assignments
ON DUPLICATE KEY UPDATE marks_obtained = VALUES(marks_obtained);

-- =====================================================
-- 7. VERIFICATION QUERIES
-- =====================================================

-- Display summary of created data
SELECT 'SUMMARY OF SAMPLE DATA CREATED' as info;

SELECT 'Teachers Created:' as category, COUNT(*) as count FROM users WHERE role = 'teacher' AND username LIKE 'teacher_%'
UNION ALL
SELECT 'Students Created:', COUNT(*) FROM students WHERE student_id LIKE 'SAMP%'
UNION ALL
SELECT 'Student User Accounts:', COUNT(*) FROM users WHERE role = 'student' AND username LIKE 'student_samp%'
UNION ALL
SELECT 'Subjects Created:', COUNT(*) FROM subjects WHERE code LIKE 'SAMP%'
UNION ALL
SELECT 'Classrooms Created:', COUNT(*) FROM classrooms WHERE session = '2024-25'
UNION ALL
SELECT 'Teacher-Subject Links:', COUNT(*) FROM teacher_subjects ts JOIN users u ON ts.teacher_id = u.id WHERE u.username LIKE 'teacher_%'
UNION ALL
SELECT 'Student-Subject Links:', COUNT(*) FROM student_subjects ss JOIN users u ON ss.student_id = u.id WHERE u.username LIKE 'student_samp%'
UNION ALL
SELECT 'Exam Attempts:', COUNT(*) FROM exam_attempts ea JOIN users u ON ea.user_id = u.id WHERE u.username LIKE 'student_samp%'
UNION ALL
SELECT 'Practical Records:', COUNT(*) FROM practical_records pr JOIN users u ON pr.student_id = u.id WHERE u.username LIKE 'student_samp%'
UNION ALL
SELECT 'Assignment Submissions:', COUNT(*) FROM assignment_submissions asub JOIN users u ON asub.student_id = u.id WHERE u.username LIKE 'student_samp%';

-- =====================================================
-- 8. QUICK TEST QUERIES
-- =====================================================

-- Test Query 1: Simple student-teacher mapping
SELECT 'TEST: Student-Teacher Mapping' as test_name;
SELECT
    s.student_id,
    s.name as student_name,
    s.class,
    s.section,
    s.trade,
    GROUP_CONCAT(DISTINCT sub.name ORDER BY sub.name) as subjects,
    GROUP_CONCAT(DISTINCT u.name ORDER BY u.name) as teachers
FROM students s
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    LEFT JOIN teacher_subjects ts ON sub.id = ts.subject_id
    LEFT JOIN users u ON ts.teacher_id = u.id AND u.role = 'teacher'
WHERE s.student_id LIKE 'SAMP%'
GROUP BY s.id
LIMIT 5;

-- Test Query 2: Student marks summary
SELECT 'TEST: Student Marks Summary' as test_name;
SELECT
    s.student_id,
    s.name as student_name,
    COUNT(DISTINCT ea.id) as exams_taken,
    ROUND(AVG(ea.score), 2) as avg_exam_score,
    COUNT(DISTINCT pr.id) as practicals_done,
    COUNT(DISTINCT asub.id) as assignments_submitted,
    ROUND(AVG(asub.marks_obtained), 2) as avg_assignment_marks
FROM students s
    LEFT JOIN users u ON LOWER(CONCAT('student_', s.student_id)) = u.username
    LEFT JOIN exam_attempts ea ON u.id = ea.user_id AND ea.status = 'completed'
    LEFT JOIN practical_records pr ON u.id = pr.student_id AND pr.status = 'graded'
    LEFT JOIN assignment_submissions asub ON u.id = asub.student_id AND asub.status = 'graded'
WHERE s.student_id LIKE 'SAMP%'
GROUP BY s.id
LIMIT 5;
