-- =====================================================================================
-- COMPREHENSIVE ACADEMIC MANAGEMENT SYSTEM - FIXED VERSION
-- =====================================================================================
-- This system handles trades, subjects, room allocation, timetables, and evaluation
-- with proper academic hierarchy and grade calculation rules
-- =====================================================================================

-- =====================================================================================
-- SECTION 1: INFRASTRUCTURE AND ROOM MANAGEMENT (FIXED)
-- =====================================================================================

-- Update existing rooms table to add missing columns
ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS room_name VARCHAR(100) AFTER room_number,
ADD COLUMN IF NOT EXISTS room_type ENUM('classroom', 'laboratory', 'library', 'auditorium') DEFAULT 'classroom' AFTER capacity,
ADD COLUMN IF NOT EXISTS facilities TEXT AFTER room_type,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE AFTER facilities;

-- Update existing rooms with room names
UPDATE rooms SET room_name = CONCAT('Classroom ', SUBSTRING(room_number, 2)) WHERE room_name IS NULL;

-- Create trades table with proper trade definitions
CREATE TABLE IF NOT EXISTS academic_trades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trade_code VARCHAR(20) NOT NULL UNIQUE,
    trade_name VARCHAR(100) NOT NULL,
    description TEXT,
    available_classes VARCHAR(50), -- e.g., '11,12'
    total_sections INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert the three trades for classes 11 and 12
INSERT IGNORE INTO academic_trades (trade_code, trade_name, description, available_classes, total_sections) VALUES
('MED', 'Medical', 'Medical stream with Biology focus', '11,12', 2),
('NON_MED', 'Non-Medical', 'Non-Medical stream with Mathematics focus', '11,12', 6),
('COMM', 'Commerce', 'Commerce stream with Business focus', '11,12', 2);

-- Create sections table
CREATE TABLE IF NOT EXISTS academic_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_code VARCHAR(10) NOT NULL,
    section_name VARCHAR(50) NOT NULL,
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_section_code (section_code)
);

-- Insert sections A through J (to cover 6 sections for Non-Medical)
INSERT IGNORE INTO academic_sections (section_code, section_name, display_order) VALUES
('A', 'Section A', 1),
('B', 'Section B', 2),
('C', 'Section C', 3),
('D', 'Section D', 4),
('E', 'Section E', 5),
('F', 'Section F', 6),
('G', 'Section G', 7),
('H', 'Section H', 8),
('I', 'Section I', 9),
('J', 'Section J', 10);

-- Create class-trade-section combinations table
CREATE TABLE IF NOT EXISTS class_trade_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_level VARCHAR(10) NOT NULL, -- '11' or '12'
    trade_id INT NOT NULL,
    section_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    total_students INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (trade_id) REFERENCES academic_trades(id),
    FOREIGN KEY (section_id) REFERENCES academic_sections(id),
    UNIQUE KEY unique_class_trade_section (class_level, trade_id, section_id, academic_session),
    INDEX idx_session_class (academic_session, class_level)
);

-- Insert class-trade-section combinations for 2023-2024
-- Medical: 2 sections (A, B)
INSERT IGNORE INTO class_trade_sections (class_level, trade_id, section_id, academic_session) VALUES
('11', 1, 1, '2023-2024'), -- Class 11 Medical A
('11', 1, 2, '2023-2024'), -- Class 11 Medical B
('12', 1, 1, '2023-2024'), -- Class 12 Medical A
('12', 1, 2, '2023-2024'), -- Class 12 Medical B

-- Non-Medical: 6 sections (A, B, C, D, E, F)
('11', 2, 1, '2023-2024'), -- Class 11 Non-Medical A
('11', 2, 2, '2023-2024'), -- Class 11 Non-Medical B
('11', 2, 3, '2023-2024'), -- Class 11 Non-Medical C
('11', 2, 4, '2023-2024'), -- Class 11 Non-Medical D
('11', 2, 5, '2023-2024'), -- Class 11 Non-Medical E
('11', 2, 6, '2023-2024'), -- Class 11 Non-Medical F
('12', 2, 1, '2023-2024'), -- Class 12 Non-Medical A
('12', 2, 2, '2023-2024'), -- Class 12 Non-Medical B
('12', 2, 3, '2023-2024'), -- Class 12 Non-Medical C
('12', 2, 4, '2023-2024'), -- Class 12 Non-Medical D
('12', 2, 5, '2023-2024'), -- Class 12 Non-Medical E
('12', 2, 6, '2023-2024'), -- Class 12 Non-Medical F

-- Commerce: 2 sections (A, B)
('11', 3, 1, '2023-2024'), -- Class 11 Commerce A
('11', 3, 2, '2023-2024'), -- Class 11 Commerce B
('12', 3, 1, '2023-2024'), -- Class 12 Commerce A
('12', 3, 2, '2023-2024'); -- Class 12 Commerce B

-- Create room allocation tracking system
CREATE TABLE IF NOT EXISTS room_allocations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_id INT NOT NULL,
    class_trade_section_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    allocation_start_date DATE,
    allocation_end_date DATE,
    allocated_by INT, -- user who made the allocation
    allocation_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES rooms(id),
    FOREIGN KEY (class_trade_section_id) REFERENCES class_trade_sections(id),
    FOREIGN KEY (allocated_by) REFERENCES users(id),
    INDEX idx_session_allocation (academic_session, is_active),
    INDEX idx_room_session (room_id, academic_session, is_active)
);

-- =====================================================================================
-- SECTION 2: SUBJECT STRUCTURE AND CURRICULUM
-- =====================================================================================

-- Enhanced subjects table with categorization
CREATE TABLE IF NOT EXISTS academic_subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_code VARCHAR(20) NOT NULL UNIQUE,
    subject_name VARCHAR(100) NOT NULL,
    subject_category ENUM('compulsory_core', 'selective', 'compulsory_language', 'additional_compulsory', 'optional') NOT NULL,
    theory_weightage DECIMAL(5,2) DEFAULT 70.00, -- percentage weightage for theory
    practical_weightage DECIMAL(5,2) DEFAULT 20.00, -- percentage weightage for practical
    cce_weightage DECIMAL(5,2) DEFAULT 10.00, -- percentage weightage for CCE
    max_theory_marks DECIMAL(6,2) DEFAULT 70.00,
    max_practical_marks DECIMAL(6,2) DEFAULT 20.00,
    max_cce_marks DECIMAL(6,2) DEFAULT 10.00,
    total_max_marks DECIMAL(6,2) DEFAULT 100.00,
    include_in_grand_total BOOLEAN DEFAULT TRUE, -- CRITICAL: Controls if subject is included in grand total
    subject_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert all subjects with proper categorization
INSERT IGNORE INTO academic_subjects (subject_code, subject_name, subject_category, theory_weightage, practical_weightage, cce_weightage, max_theory_marks, max_practical_marks, max_cce_marks, include_in_grand_total, subject_order) VALUES
-- Compulsory Core Subjects
('PHY', 'Physics', 'compulsory_core', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 1),
('CHEM', 'Chemistry', 'compulsory_core', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 2),
('MATH', 'Mathematics', 'compulsory_core', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 3),
('BIO', 'Biology', 'compulsory_core', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 4),
('ACC', 'Accountancy', 'compulsory_core', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 5),
('BS', 'Business Studies', 'compulsory_core', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 6),

-- Selective Subjects
('ECO', 'Economics', 'selective', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 7),
('MOP', 'Methods of Production', 'selective', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 8),
('EBUS', 'E-Business', 'selective', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 9),

-- Compulsory Language Subjects
('PUN', 'Punjabi', 'compulsory_language', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 10),
('ENG', 'English', 'compulsory_language', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 11),

-- Additional Compulsory Subjects (NOT included in grand total)
('CS', 'Computer Science', 'additional_compulsory', 60.00, 30.00, 10.00, 60.00, 30.00, 10.00, FALSE, 12),
('EVS', 'Environmental Science', 'additional_compulsory', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, FALSE, 13),

-- Optional Subjects (NOT included in grand total)
('BIO_OPT', 'Biology (Optional)', 'optional', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, FALSE, 14),
('MATH_OPT', 'Mathematics (Optional)', 'optional', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, FALSE, 15);

-- Create trade-subject combinations table
CREATE TABLE IF NOT EXISTS trade_subject_combinations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trade_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_level VARCHAR(10) NOT NULL, -- '11' or '12'
    subject_type ENUM('compulsory_core', 'selective_option', 'compulsory_language', 'additional_compulsory', 'optional') NOT NULL,
    is_mandatory BOOLEAN DEFAULT TRUE,
    academic_session VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trade_id) REFERENCES academic_trades(id),
    FOREIGN KEY (subject_id) REFERENCES academic_subjects(id),
    UNIQUE KEY unique_trade_subject_class (trade_id, subject_id, class_level, academic_session),
    INDEX idx_trade_class_session (trade_id, class_level, academic_session)
);

-- =====================================================================================
-- SECTION 3: COMPREHENSIVE VIEWS FOR THE NEW SYSTEM
-- =====================================================================================

-- View 1: Complete Trade-Subject Curriculum
CREATE OR REPLACE VIEW v_trade_curriculum AS
SELECT
    at.trade_code,
    at.trade_name,
    tsc.class_level,
    tsc.academic_session,
    asub.subject_code,
    asub.subject_name,
    asub.subject_category,
    tsc.subject_type,
    tsc.is_mandatory,
    asub.include_in_grand_total,
    asub.theory_weightage,
    asub.practical_weightage,
    asub.cce_weightage,
    asub.total_max_marks,
    CASE
        WHEN tsc.subject_type = 'compulsory_core' THEN 'Core Subject'
        WHEN tsc.subject_type = 'selective_option' THEN 'Choose One'
        WHEN tsc.subject_type = 'compulsory_language' THEN 'Language'
        WHEN tsc.subject_type = 'additional_compulsory' THEN 'Additional (Not in Total)'
        WHEN tsc.subject_type = 'optional' THEN 'Optional (Not in Total)'
        ELSE 'Other'
    END AS subject_type_description
FROM academic_trades at
JOIN trade_subject_combinations tsc ON at.id = tsc.trade_id
JOIN academic_subjects asub ON tsc.subject_id = asub.id
ORDER BY at.trade_code, tsc.class_level, asub.subject_order, asub.subject_name;

-- View 2: Class-Trade-Section with Room Allocations
CREATE OR REPLACE VIEW v_class_allocations AS
SELECT
    cts.id AS class_trade_section_id,
    cts.class_level,
    at.trade_code,
    at.trade_name,
    asec.section_code,
    asec.section_name,
    cts.academic_session,
    cts.total_students,
    r.room_number,
    COALESCE(r.room_name, CONCAT('Room ', r.room_number)) AS room_name,
    r.capacity,
    ra.allocation_start_date,
    ra.allocation_end_date,
    ra.is_active AS allocation_active,
    CASE
        WHEN ra.id IS NOT NULL THEN 'Allocated'
        ELSE 'Not Allocated'
    END AS allocation_status
FROM class_trade_sections cts
JOIN academic_trades at ON cts.trade_id = at.id
JOIN academic_sections asec ON cts.section_id = asec.id
LEFT JOIN room_allocations ra ON cts.id = ra.class_trade_section_id AND ra.is_active = TRUE
LEFT JOIN rooms r ON ra.room_id = r.id
ORDER BY cts.academic_session, cts.class_level, at.trade_code, asec.section_code;

-- View 3: Enhanced Student Marks with Trade-Subject Integration
CREATE OR REPLACE VIEW v_enhanced_student_marks AS
SELECT
    s.student_id AS roll_number,
    s.name AS student_name,
    s.father_name,
    cts.class_level,
    at.trade_code,
    at.trade_name,
    asec.section_code,
    ssm.academic_session,
    asub.subject_code,
    asub.subject_name,
    asub.subject_category,
    e.exam_name,

    -- Marks components
    ssm.theory_marks,
    ssm.practical_marks,
    ssm.internal_marks AS cce_marks,
    ssm.total_marks,
    asub.total_max_marks AS max_marks,
    ROUND(((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100, 2) AS percentage,

    -- Grade calculation
    CASE
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 >= 90 THEN 'A+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 >= 80 THEN 'A'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 >= 70 THEN 'B+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 >= 60 THEN 'B'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 >= 50 THEN 'C+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 >= 40 THEN 'C'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 >= 33 THEN 'D'
        ELSE 'F'
    END AS grade,

    -- Subject categorization for grade calculation
    asub.include_in_grand_total,
    CASE
        WHEN asub.include_in_grand_total = TRUE THEN 'Counts in Total'
        ELSE 'Additional Subject'
    END AS grade_calculation_status,

    -- Pass/Fail status
    CASE
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 >= 33
        THEN 'PASS'
        ELSE 'FAIL'
    END AS result_status,

    ssm.remarks,
    ssm.created_at AS marks_entry_date

FROM students s
JOIN student_subject_marks ssm ON s.id = ssm.student_id
JOIN academic_subjects asub ON ssm.subject_id = asub.id
JOIN exams e ON ssm.exam_id = e.exam_id
LEFT JOIN class_trade_sections cts ON s.class = cts.class_level
    AND s.trade = (SELECT trade_code FROM academic_trades WHERE id = cts.trade_id)
    AND s.section = (SELECT section_code FROM academic_sections WHERE id = cts.section_id)
    AND ssm.academic_session = cts.academic_session
LEFT JOIN academic_trades at ON cts.trade_id = at.id
LEFT JOIN academic_sections asec ON cts.section_id = asec.id
ORDER BY s.student_id, ssm.academic_session, asub.subject_order, e.exam_name;

-- View 4: Student Grand Total Calculation (Only subjects that count)
CREATE OR REPLACE VIEW v_student_grand_totals AS
SELECT
    s.student_id AS roll_number,
    s.name AS student_name,
    cts.class_level,
    at.trade_code,
    at.trade_name,
    asec.section_code,
    ssm.academic_session,
    e.exam_name,

    -- Grand total calculation (only subjects that count)
    COUNT(CASE WHEN asub.include_in_grand_total = TRUE THEN 1 END) AS subjects_in_total,
    SUM(CASE WHEN asub.include_in_grand_total = TRUE THEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) ELSE 0 END) AS grand_total_marks,
    SUM(CASE WHEN asub.include_in_grand_total = TRUE THEN asub.total_max_marks ELSE 0 END) AS grand_max_marks,
    ROUND((SUM(CASE WHEN asub.include_in_grand_total = TRUE THEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) ELSE 0 END) /
           SUM(CASE WHEN asub.include_in_grand_total = TRUE THEN asub.total_max_marks ELSE 0 END)) * 100, 2) AS grand_percentage,

    -- Additional subjects (not counted in total)
    COUNT(CASE WHEN asub.include_in_grand_total = FALSE THEN 1 END) AS additional_subjects,
    SUM(CASE WHEN asub.include_in_grand_total = FALSE THEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) ELSE 0 END) AS additional_total_marks,
    SUM(CASE WHEN asub.include_in_grand_total = FALSE THEN asub.total_max_marks ELSE 0 END) AS additional_max_marks,

    -- Pass/Fail status
    SUM(CASE WHEN asub.include_in_grand_total = TRUE AND ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 < 33 THEN 1 ELSE 0 END) AS failed_core_subjects,
    CASE
        WHEN SUM(CASE WHEN asub.include_in_grand_total = TRUE AND ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / asub.total_max_marks) * 100 < 33 THEN 1 ELSE 0 END) = 0 THEN 'PASS'
        ELSE 'FAIL'
    END AS overall_result

FROM students s
JOIN student_subject_marks ssm ON s.id = ssm.student_id
JOIN academic_subjects asub ON ssm.subject_id = asub.id
JOIN exams e ON ssm.exam_id = e.exam_id
LEFT JOIN class_trade_sections cts ON s.class = cts.class_level
    AND s.trade = (SELECT trade_code FROM academic_trades WHERE id = cts.trade_id)
    AND s.section = (SELECT section_code FROM academic_sections WHERE id = cts.section_id)
    AND ssm.academic_session = cts.academic_session
LEFT JOIN academic_trades at ON cts.trade_id = at.id
LEFT JOIN academic_sections asec ON cts.section_id = asec.id
GROUP BY s.id, cts.id, ssm.academic_session, e.exam_id
ORDER BY s.student_id, ssm.academic_session, e.exam_name;

-- =====================================================================================
-- DEMONSTRATION QUERIES
-- =====================================================================================

-- Show all class-trade-section combinations
SELECT 'Class-Trade-Section Combinations:' AS info;
SELECT * FROM v_class_allocations ORDER BY class_level, trade_code, section_code;

-- Show trade curriculum structure
SELECT 'Trade Curriculum Structure:' AS info;
SELECT trade_code, trade_name, class_level, subject_code, subject_name, subject_type_description, include_in_grand_total
FROM v_trade_curriculum
WHERE academic_session = '2023-2024'
ORDER BY trade_code, class_level, asub.subject_order;
