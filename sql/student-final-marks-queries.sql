-- REQUIREMENT 2: Student Final Marks - Theory, CCE, Practical, Total, Percentage, Grade, Session
-- Comprehensive queries for student final marks system

-- Query 1: Complete Student Final Marks Report
-- Shows all components: Theory, CCE (Internal), Practical, Total, Percentage, Grade, Session
SELECT 
    s.student_id AS 'Roll Number',
    s.name AS 'Student Name',
    s.father_name AS 'Father Name',
    s.class AS 'Class',
    s.section AS 'Section',
    s.trade AS 'Trade',
    s.session AS 'Academic Session',
    sub.name AS 'Subject',
    sub.code AS 'Subject Code',
    e.exam_name AS 'Exam',
    ssm.theory_marks AS 'Theory Marks',
    ssm.internal_marks AS 'CCE/Internal Marks',
    ssm.practical_marks AS 'Practical Marks',
    ssm.total_marks AS 'Total Marks',
    ssm.max_marks AS 'Maximum Marks',
    ssm.percentage AS 'Percentage',
    ssm.grade AS 'Grade',
    CASE WHEN ssm.is_pass = 1 THEN 'PASS' ELSE 'FAIL' END AS 'Result',
    ssm.academic_session AS 'Marks Session'
FROM 
    students s
    JOIN student_subject_marks ssm ON s.id = ssm.student_id
    JOIN subjects sub ON ssm.subject_id = sub.id
    JOIN exams e ON ssm.exam_id = e.exam_id
ORDER BY 
    s.student_id, sub.name, e.exam_name;

-- Query 2: Student-wise Subject Performance Summary
-- Aggregated view showing overall performance per student per subject
SELECT 
    s.student_id AS 'Roll Number',
    s.name AS 'Student Name',
    s.class AS 'Class',
    s.section AS 'Section',
    s.session AS 'Academic Session',
    sub.name AS 'Subject',
    COUNT(ssm.id) AS 'Total Exams',
    ROUND(AVG(ssm.theory_marks), 2) AS 'Avg Theory',
    ROUND(AVG(ssm.practical_marks), 2) AS 'Avg Practical',
    ROUND(AVG(ssm.internal_marks), 2) AS 'Avg CCE/Internal',
    ROUND(AVG(ssm.total_marks), 2) AS 'Avg Total',
    ROUND(AVG(ssm.percentage), 2) AS 'Avg Percentage',
    SUM(CASE WHEN ssm.is_pass = 1 THEN 1 ELSE 0 END) AS 'Exams Passed',
    SUM(CASE WHEN ssm.is_pass = 0 THEN 1 ELSE 0 END) AS 'Exams Failed',
    CASE 
        WHEN AVG(ssm.percentage) >= 90 THEN 'A+'
        WHEN AVG(ssm.percentage) >= 80 THEN 'A'
        WHEN AVG(ssm.percentage) >= 70 THEN 'B+'
        WHEN AVG(ssm.percentage) >= 60 THEN 'B'
        WHEN AVG(ssm.percentage) >= 50 THEN 'C+'
        WHEN AVG(ssm.percentage) >= 40 THEN 'C'
        WHEN AVG(ssm.percentage) >= 33 THEN 'D'
        ELSE 'F'
    END AS 'Overall Grade'
FROM 
    students s
    JOIN student_subject_marks ssm ON s.id = ssm.student_id
    JOIN subjects sub ON ssm.subject_id = sub.id
GROUP BY 
    s.id, sub.id
ORDER BY 
    s.student_id, sub.name;

-- Query 3: Class-wise Performance Analysis
-- Shows performance statistics by class and section
SELECT 
    s.class AS 'Class',
    s.section AS 'Section',
    s.session AS 'Academic Session',
    COUNT(DISTINCT s.id) AS 'Total Students',
    COUNT(ssm.id) AS 'Total Mark Entries',
    COUNT(DISTINCT ssm.subject_id) AS 'Subjects Covered',
    ROUND(AVG(ssm.theory_marks), 2) AS 'Avg Theory Marks',
    ROUND(AVG(ssm.practical_marks), 2) AS 'Avg Practical Marks',
    ROUND(AVG(ssm.internal_marks), 2) AS 'Avg CCE/Internal Marks',
    ROUND(AVG(ssm.percentage), 2) AS 'Class Average %',
    MAX(ssm.percentage) AS 'Highest %',
    MIN(ssm.percentage) AS 'Lowest %',
    SUM(CASE WHEN ssm.is_pass = 1 THEN 1 ELSE 0 END) AS 'Total Passes',
    SUM(CASE WHEN ssm.is_pass = 0 THEN 1 ELSE 0 END) AS 'Total Failures',
    ROUND((SUM(CASE WHEN ssm.is_pass = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(ssm.id)), 2) AS 'Pass Rate %'
FROM 
    students s
    JOIN student_subject_marks ssm ON s.id = ssm.student_id
GROUP BY 
    s.class, s.section, s.session
ORDER BY 
    s.class, s.section;

-- Query 4: Subject-wise Performance Analysis
-- Shows how students perform in each subject across all classes
SELECT 
    sub.name AS 'Subject',
    sub.code AS 'Subject Code',
    COUNT(DISTINCT ssm.student_id) AS 'Students Enrolled',
    COUNT(ssm.id) AS 'Total Assessments',
    ROUND(AVG(ssm.theory_marks), 2) AS 'Avg Theory',
    ROUND(AVG(ssm.practical_marks), 2) AS 'Avg Practical',
    ROUND(AVG(ssm.internal_marks), 2) AS 'Avg CCE/Internal',
    ROUND(AVG(ssm.percentage), 2) AS 'Subject Average %',
    MAX(ssm.percentage) AS 'Highest Score %',
    MIN(ssm.percentage) AS 'Lowest Score %',
    SUM(CASE WHEN ssm.is_pass = 1 THEN 1 ELSE 0 END) AS 'Passes',
    SUM(CASE WHEN ssm.is_pass = 0 THEN 1 ELSE 0 END) AS 'Failures',
    ROUND((SUM(CASE WHEN ssm.is_pass = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(ssm.id)), 2) AS 'Pass Rate %'
FROM 
    subjects sub
    JOIN student_subject_marks ssm ON sub.id = ssm.subject_id
GROUP BY 
    sub.id
ORDER BY 
    sub.name;

-- Query 5: Individual Student Report Card
-- Detailed report for a specific student (example: student_id = 'STU001')
SELECT 
    'STUDENT REPORT CARD' AS 'Report Type',
    s.student_id AS 'Roll Number',
    s.name AS 'Student Name',
    s.father_name AS 'Father Name',
    s.mother_name AS 'Mother Name',
    s.class AS 'Class',
    s.section AS 'Section',
    s.trade AS 'Trade',
    s.session AS 'Academic Session'
FROM students s WHERE s.student_id = 'STU001'

UNION ALL

SELECT 
    'MARKS DETAILS' AS 'Report Type',
    sub.name AS 'Subject',
    e.exam_name AS 'Exam',
    CONCAT(ssm.theory_marks, '/', ssm.max_marks) AS 'Theory',
    CONCAT(ssm.practical_marks, '/', ssm.max_marks) AS 'Practical',
    CONCAT(ssm.internal_marks, '/', ssm.max_marks) AS 'CCE/Internal',
    CONCAT(ssm.total_marks, '/', ssm.max_marks) AS 'Total',
    CONCAT(ssm.percentage, '%') AS 'Percentage',
    ssm.grade AS 'Grade',
    CASE WHEN ssm.is_pass = 1 THEN 'PASS' ELSE 'FAIL' END AS 'Result'
FROM 
    students s
    JOIN student_subject_marks ssm ON s.id = ssm.student_id
    JOIN subjects sub ON ssm.subject_id = sub.id
    JOIN exams e ON ssm.exam_id = e.exam_id
WHERE s.student_id = 'STU001'
ORDER BY 'Report Type', 'Subject';
