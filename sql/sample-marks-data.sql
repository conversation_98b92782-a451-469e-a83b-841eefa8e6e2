-- Sample data script to test student final marks system
-- This script creates sample marks data for testing the views and functionality

-- Start transaction
START TRANSACTION;

-- Insert sample marks data for testing
-- Student ID 1 (<PERSON><PERSON><PERSON>) - Mathematics and English marks
INSERT IGNORE INTO student_subject_marks 
(student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session)
VALUES 
-- Mathematics marks for different exams
(1, 264, 1, 85.00, 0.00, 15.00, 100.00, 100.00, 100.00, 'A+', 1, '2023-2024'),
(1, 316, 1, 78.00, 0.00, 12.00, 90.00, 100.00, 90.00, 'A+', 1, '2023-2024'),

-- English marks
(1, 264, 2, 75.00, 0.00, 20.00, 95.00, 100.00, 95.00, 'A+', 1, '2023-2024'),
(1, 316, 2, 68.00, 0.00, 17.00, 85.00, 100.00, 85.00, 'A', 1, '2023-2024'),

-- Chemistry marks
(1, 264, 2, 82.00, 15.00, 18.00, 115.00, 120.00, 95.83, 'A+', 1, '2023-2024'),

-- Student ID 2 (<PERSON><PERSON> <PERSON>) - <PERSON><PERSON> marks
(2, 264, 1, 72.00, 0.00, 18.00, 90.00, 100.00, 90.00, 'A+', 1, '2023-2024'),
(2, 264, 2, 65.00, 0.00, 15.00, 80.00, 100.00, 80.00, 'A', 1, '2023-2024'),
(2, 316, 1, 68.00, 0.00, 16.00, 84.00, 100.00, 84.00, 'A', 1, '2023-2024'),

-- Student ID 3 (Arjun Singh) - Sample marks including some failures
(3, 264, 1, 45.00, 0.00, 10.00, 55.00, 100.00, 55.00, 'C+', 1, '2023-2024'),
(3, 264, 2, 25.00, 0.00, 8.00, 33.00, 100.00, 33.00, 'D', 1, '2023-2024'),
(3, 316, 1, 30.00, 0.00, 5.00, 35.00, 100.00, 35.00, 'D', 1, '2023-2024'),
(3, 316, 2, 20.00, 0.00, 5.00, 25.00, 100.00, 25.00, 'F', 0, '2023-2024'),

-- Student ID 4 (Kavya Reddy) - Excellent performance
(4, 264, 1, 95.00, 0.00, 20.00, 115.00, 120.00, 95.83, 'A+', 1, '2023-2024'),
(4, 264, 2, 88.00, 0.00, 17.00, 105.00, 110.00, 95.45, 'A+', 1, '2023-2024'),
(4, 264, 4, 92.00, 18.00, 15.00, 125.00, 130.00, 96.15, 'A+', 1, '2023-2024'),

-- Student ID 5 (Rohan Kumar) - Mixed performance
(5, 264, 1, 58.00, 0.00, 12.00, 70.00, 100.00, 70.00, 'B+', 1, '2023-2024'),
(5, 264, 2, 42.00, 0.00, 8.00, 50.00, 100.00, 50.00, 'C+', 1, '2023-2024'),
(5, 316, 1, 62.00, 0.00, 13.00, 75.00, 100.00, 75.00, 'B+', 1, '2023-2024');

-- Update calculated fields based on the marks
UPDATE student_subject_marks 
SET 
    total_marks = theory_marks + practical_marks + internal_marks,
    percentage = ROUND(((theory_marks + practical_marks + internal_marks) / max_marks) * 100, 2),
    is_pass = CASE WHEN ((theory_marks + practical_marks + internal_marks) / max_marks) * 100 >= 33 THEN 1 ELSE 0 END,
    grade = CASE 
        WHEN ((theory_marks + practical_marks + internal_marks) / max_marks) * 100 >= 90 THEN 'A+'
        WHEN ((theory_marks + practical_marks + internal_marks) / max_marks) * 100 >= 80 THEN 'A'
        WHEN ((theory_marks + practical_marks + internal_marks) / max_marks) * 100 >= 70 THEN 'B+'
        WHEN ((theory_marks + practical_marks + internal_marks) / max_marks) * 100 >= 60 THEN 'B'
        WHEN ((theory_marks + practical_marks + internal_marks) / max_marks) * 100 >= 50 THEN 'C+'
        WHEN ((theory_marks + practical_marks + internal_marks) / max_marks) * 100 >= 40 THEN 'C'
        WHEN ((theory_marks + practical_marks + internal_marks) / max_marks) * 100 >= 33 THEN 'D'
        ELSE 'F'
    END
WHERE id > 0;

-- Commit the transaction
COMMIT;

-- Show success message
SELECT 'Sample marks data inserted successfully!' AS message;

-- Show summary of inserted data
SELECT COUNT(*) AS total_marks_records FROM student_subject_marks;
SELECT COUNT(DISTINCT student_id) AS students_with_marks FROM student_subject_marks;
SELECT COUNT(DISTINCT subject_id) AS subjects_with_marks FROM student_subject_marks;
SELECT COUNT(DISTINCT exam_id) AS exams_with_marks FROM student_subject_marks;

-- Test the views with sample data
SELECT 'Testing student_performance_summary view:' AS test_message;
SELECT * FROM student_performance_summary LIMIT 5;

SELECT 'Testing student_final_marks view (first 5 records):' AS test_message;
SELECT student_id, roll_number, student_name, subject_name, theory_marks, practical_marks, total_marks, percentage, grade, result_status 
FROM student_final_marks 
LIMIT 5;
