-- =====================================================================================
-- COMPREHENSIVE STUDENT MARKS ANALYSIS SYSTEM - DEMONSTRATION
-- =====================================================================================
-- This script demonstrates all the key features of the comprehensive marks analysis system
-- =====================================================================================

-- =====================================================================================
-- DEMONSTRATION 1: COMPREHENSIVE FINAL MARKS VIEW
-- =====================================================================================

SELECT 'DEMONSTRATION 1: COMPREHENSIVE FINAL MARKS VIEW' AS demo_title;

SELECT 
    roll_number,
    student_name,
    class,
    section,
    trade,
    academic_session,
    subject_name,
    exam_name,
    theory_marks,
    practical_marks,
    cce_marks,
    calculated_total_marks,
    calculated_percentage,
    calculated_grade,
    result_status
FROM comprehensive_final_marks 
WHERE academic_session = '2023-2024'
ORDER BY roll_number, subject_name, exam_name;

-- =====================================================================================
-- DEMONSTRATION 2: STUDENT PERFORMANCE TRACKING
-- =====================================================================================

SELECT 'DEMONSTRATION 2: STUDENT PERFORMANCE SUMMARY BY SESSION' AS demo_title;

SELECT 
    roll_number,
    student_name,
    class,
    section,
    trade,
    academic_session,
    total_subjects,
    session_average_percentage,
    highest_percentage,
    lowest_percentage,
    exams_passed,
    exams_failed,
    pass_rate,
    session_result
FROM student_session_summary
ORDER BY academic_session, session_average_percentage DESC;

-- =====================================================================================
-- DEMONSTRATION 3: TOPPERS IDENTIFICATION
-- =====================================================================================

SELECT 'DEMONSTRATION 3A: SESSION TOPPERS (TOP 5)' AS demo_title;

SELECT 
    academic_session,
    overall_rank,
    roll_number,
    student_name,
    class,
    section,
    trade,
    session_average_percentage,
    highest_percentage,
    excellent_grades,
    session_result
FROM session_toppers 
WHERE academic_session = '2023-2024'
ORDER BY overall_rank;

SELECT 'DEMONSTRATION 3B: CLASS TOPPERS' AS demo_title;

SELECT 
    academic_session,
    class,
    section,
    class_rank,
    roll_number,
    student_name,
    trade,
    session_average_percentage,
    session_result
FROM class_toppers 
WHERE academic_session = '2023-2024'
ORDER BY class, section, class_rank;

-- =====================================================================================
-- DEMONSTRATION 4: MULTI-DIMENSIONAL ANALYSIS
-- =====================================================================================

SELECT 'DEMONSTRATION 4A: CLASS PERFORMANCE ANALYSIS' AS demo_title;

SELECT 
    academic_session,
    class,
    section,
    total_students,
    class_average_percentage,
    highest_score,
    lowest_score,
    pass_rate,
    class_performance_rating
FROM class_performance_analysis 
WHERE academic_session = '2023-2024'
ORDER BY class_average_percentage DESC;

SELECT 'DEMONSTRATION 4B: TRADE PERFORMANCE ANALYSIS' AS demo_title;

SELECT 
    academic_session,
    trade,
    total_students,
    trade_average_percentage,
    highest_score,
    lowest_score,
    pass_rate,
    trade_rank_in_session,
    trade_performance_rating
FROM trade_performance_analysis 
WHERE academic_session = '2023-2024'
ORDER BY trade_rank_in_session;

-- =====================================================================================
-- DEMONSTRATION 5: SUBJECT PERFORMANCE ANALYSIS
-- =====================================================================================

SELECT 'DEMONSTRATION 5A: SUBJECT PERFORMANCE STATISTICS' AS demo_title;

SELECT 
    academic_session,
    subject_name,
    students_enrolled,
    total_assessments,
    subject_average_percentage,
    highest_score,
    lowest_score,
    pass_rate,
    perfect_scorers_count,
    subject_difficulty_rating,
    subject_rank_in_session
FROM subject_performance_statistics 
WHERE academic_session = '2023-2024'
ORDER BY subject_rank_in_session;

SELECT 'DEMONSTRATION 5B: PERFECT SCORERS (100% ACHIEVERS)' AS demo_title;

SELECT 
    academic_session,
    subject_name,
    exam_name,
    roll_number,
    student_name,
    class,
    section,
    trade,
    calculated_percentage,
    calculated_grade
FROM perfect_scorers 
WHERE academic_session = '2023-2024'
ORDER BY subject_name, student_name;

-- =====================================================================================
-- DEMONSTRATION 6: MARKS DISTRIBUTION ANALYSIS
-- =====================================================================================

SELECT 'DEMONSTRATION 6: MARKS DISTRIBUTION BY RANGES' AS demo_title;

SELECT 
    academic_session,
    range_name,
    CONCAT(min_marks, '-', max_marks) AS marks_range,
    students_in_range,
    percentage_of_total,
    subjects_represented,
    classes_represented,
    avg_percentage_in_range
FROM marks_distribution_analysis 
WHERE academic_session = '2023-2024'
ORDER BY display_order;

-- =====================================================================================
-- DEMONSTRATION 7: INDIVIDUAL STUDENT DETAILED REPORT
-- =====================================================================================

SELECT 'DEMONSTRATION 7: INDIVIDUAL STUDENT PERFORMANCE HISTORY' AS demo_title;

SELECT 
    roll_number,
    student_name,
    academic_session,
    subject_name,
    exam_name,
    calculated_percentage,
    previous_exam_percentage,
    percentage_change,
    subject_rank_in_session,
    calculated_grade,
    result_status
FROM student_performance_history 
WHERE roll_number = 'STU001'
ORDER BY academic_session, subject_name, exam_name;

-- =====================================================================================
-- DEMONSTRATION 8: GRADE MAPPING AND DISTRIBUTION
-- =====================================================================================

SELECT 'DEMONSTRATION 8A: GRADE MAPPING CONFIGURATION' AS demo_title;

SELECT 
    academic_session,
    grade,
    min_percentage,
    max_percentage,
    grade_points,
    description
FROM grade_mapping 
WHERE academic_session = '2023-2024'
ORDER BY min_percentage DESC;

SELECT 'DEMONSTRATION 8B: GRADE DISTRIBUTION SUMMARY' AS demo_title;

SELECT 
    academic_session,
    calculated_grade,
    COUNT(*) AS student_count,
    ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM comprehensive_final_marks WHERE academic_session = '2023-2024')), 2) AS percentage_of_total
FROM comprehensive_final_marks 
WHERE academic_session = '2023-2024'
GROUP BY academic_session, calculated_grade
ORDER BY 
    CASE calculated_grade 
        WHEN 'A+' THEN 1 
        WHEN 'A' THEN 2 
        WHEN 'B+' THEN 3 
        WHEN 'B' THEN 4 
        WHEN 'C+' THEN 5 
        WHEN 'C' THEN 6 
        WHEN 'D' THEN 7 
        WHEN 'F' THEN 8 
        ELSE 9 
    END;

-- =====================================================================================
-- DEMONSTRATION SUMMARY
-- =====================================================================================

SELECT 'SYSTEM DEMONSTRATION COMPLETED SUCCESSFULLY!' AS summary_message;

SELECT 
    'Total Students Analyzed' AS metric,
    COUNT(DISTINCT roll_number) AS value
FROM comprehensive_final_marks 
WHERE academic_session = '2023-2024'

UNION ALL

SELECT 
    'Total Subjects Covered' AS metric,
    COUNT(DISTINCT subject_name) AS value
FROM comprehensive_final_marks 
WHERE academic_session = '2023-2024'

UNION ALL

SELECT 
    'Total Exam Instances' AS metric,
    COUNT(*) AS value
FROM comprehensive_final_marks 
WHERE academic_session = '2023-2024'

UNION ALL

SELECT 
    'Overall Pass Rate (%)' AS metric,
    ROUND((SUM(CASE WHEN result_status = 'PASS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2) AS value
FROM comprehensive_final_marks 
WHERE academic_session = '2023-2024'

UNION ALL

SELECT 
    'Average Session Performance (%)' AS metric,
    ROUND(AVG(calculated_percentage), 2) AS value
FROM comprehensive_final_marks 
WHERE academic_session = '2023-2024';

SELECT 'All views and queries are working correctly with sample data!' AS final_status;
