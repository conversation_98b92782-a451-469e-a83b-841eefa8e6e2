-- Individual Student Report Card Query (Fixed)
-- Detailed report for a specific student

-- Student Basic Information
SELECT 
    'STUDENT INFORMATION' AS 'Section',
    'Roll Number' AS 'Field',
    s.student_id AS 'Value'
FROM students s WHERE s.student_id = 'STU001'

UNION ALL

SELECT 
    'STUDENT INFORMATION' AS 'Section',
    'Student Name' AS 'Field',
    s.name AS 'Value'
FROM students s WHERE s.student_id = 'STU001'

UNION ALL

SELECT 
    'STUDENT INFORMATION' AS 'Section',
    'Father Name' AS 'Field',
    s.father_name AS 'Value'
FROM students s WHERE s.student_id = 'STU001'

UNION ALL

SELECT 
    'STUDENT INFORMATION' AS 'Section',
    'Class' AS 'Field',
    s.class AS 'Value'
FROM students s WHERE s.student_id = 'STU001'

UNION ALL

SELECT 
    'STUDENT INFORMATION' AS 'Section',
    'Section' AS 'Field',
    s.section AS 'Value'
FROM students s WHERE s.student_id = 'STU001'

UNION ALL

SELECT 
    'STUDENT INFORMATION' AS 'Section',
    'Trade' AS 'Field',
    s.trade AS 'Value'
FROM students s WHERE s.student_id = 'STU001'

UNION ALL

SELECT 
    'STUDENT INFORMATION' AS 'Section',
    'Academic Session' AS 'Field',
    s.session AS 'Value'
FROM students s WHERE s.student_id = 'STU001';

-- Separate query for marks details
SELECT 
    'MARKS BREAKDOWN' AS 'Report Section',
    sub.name AS 'Subject',
    e.exam_name AS 'Exam',
    ssm.theory_marks AS 'Theory',
    ssm.practical_marks AS 'Practical', 
    ssm.internal_marks AS 'CCE/Internal',
    ssm.total_marks AS 'Total',
    ssm.max_marks AS 'Max Marks',
    ssm.percentage AS 'Percentage',
    ssm.grade AS 'Grade',
    CASE WHEN ssm.is_pass = 1 THEN 'PASS' ELSE 'FAIL' END AS 'Result'
FROM 
    students s
    JOIN student_subject_marks ssm ON s.id = ssm.student_id
    JOIN subjects sub ON ssm.subject_id = sub.id
    JOIN exams e ON ssm.exam_id = e.exam_id
WHERE s.student_id = 'STU001'
ORDER BY sub.name, e.exam_name;

-- Student Performance Summary
SELECT 
    'PERFORMANCE SUMMARY' AS 'Report Section',
    sub.name AS 'Subject',
    COUNT(ssm.id) AS 'Total Exams',
    ROUND(AVG(ssm.theory_marks), 2) AS 'Avg Theory',
    ROUND(AVG(ssm.practical_marks), 2) AS 'Avg Practical',
    ROUND(AVG(ssm.internal_marks), 2) AS 'Avg CCE',
    ROUND(AVG(ssm.total_marks), 2) AS 'Avg Total',
    ROUND(AVG(ssm.percentage), 2) AS 'Avg Percentage',
    SUM(CASE WHEN ssm.is_pass = 1 THEN 1 ELSE 0 END) AS 'Exams Passed',
    SUM(CASE WHEN ssm.is_pass = 0 THEN 1 ELSE 0 END) AS 'Exams Failed',
    CASE 
        WHEN AVG(ssm.percentage) >= 90 THEN 'A+'
        WHEN AVG(ssm.percentage) >= 80 THEN 'A'
        WHEN AVG(ssm.percentage) >= 70 THEN 'B+'
        WHEN AVG(ssm.percentage) >= 60 THEN 'B'
        WHEN AVG(ssm.percentage) >= 50 THEN 'C+'
        WHEN AVG(ssm.percentage) >= 40 THEN 'C'
        WHEN AVG(ssm.percentage) >= 33 THEN 'D'
        ELSE 'F'
    END AS 'Overall Grade'
FROM 
    students s
    JOIN student_subject_marks ssm ON s.id = ssm.student_id
    JOIN subjects sub ON ssm.subject_id = sub.id
WHERE s.student_id = 'STU001'
GROUP BY sub.id
ORDER BY sub.name;
