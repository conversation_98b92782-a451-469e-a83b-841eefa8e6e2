-- Final script to complete database structure update
-- This script handles the remaining tasks after the previous script

-- Start transaction
START TRANSACTION;

-- First, let's check the existing class_sections table structure and work with it
-- The existing class_sections table has: id, class_id, trade_id, section, created_at, updated_at

-- For now, let's create a simple mapping table for sections if it doesn't exist
CREATE TABLE IF NOT EXISTS simple_sections (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(10) NOT NULL UNIQUE,
  display_name VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Populate simple_sections with existing section data from students table
INSERT IGNORE INTO simple_sections (name, display_name)
SELECT DISTINCT section, CONCAT('Section ', section)
FROM students
WHERE section IS NOT NULL AND section != '';

-- Now update section_id based on section name (using simple_sections table)
UPDATE students s
JOIN simple_sections ss ON s.section = ss.name
SET s.section_id = ss.id
WHERE s.section_id IS NULL AND s.section IS NOT NULL;

-- Add indexes for better performance
ALTER TABLE students ADD INDEX IF NOT EXISTS idx_student_academic_session (academic_session);
ALTER TABLE classes ADD INDEX IF NOT EXISTS idx_class_academic_session (academic_session);
ALTER TABLE student_classes ADD INDEX IF NOT EXISTS idx_student_class_academic_session (academic_session);
ALTER TABLE student_subjects ADD INDEX IF NOT EXISTS idx_student_subject_academic_session (academic_session);
ALTER TABLE subject_trade_combinations ADD INDEX IF NOT EXISTS idx_subject_trade_academic_session (academic_session);
ALTER TABLE exams ADD INDEX IF NOT EXISTS idx_exam_academic_session (academic_session);
ALTER TABLE exam_attempts ADD INDEX IF NOT EXISTS idx_exam_attempt_academic_session (academic_session);
ALTER TABLE exam_subjects ADD INDEX IF NOT EXISTS idx_exam_subject_academic_session (academic_session);
ALTER TABLE student_subject_marks ADD INDEX IF NOT EXISTS idx_student_marks_academic_session (academic_session);
ALTER TABLE teacher_classes ADD INDEX IF NOT EXISTS idx_teacher_class_academic_session (academic_session);
ALTER TABLE teacher_subjects ADD INDEX IF NOT EXISTS idx_teacher_subject_academic_session (academic_session);
ALTER TABLE class_incharge ADD INDEX IF NOT EXISTS idx_class_incharge_academic_session (academic_session);

-- Create a view for student report cards (updated to work with existing structure)
CREATE OR REPLACE VIEW student_report_cards AS
SELECT 
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.father_name,
    s.mother_name,
    c.grade AS class_name,
    cs.name AS section_name,
    s.academic_session,
    t.name AS trade_name,
    str.name AS stream_name,
    e.exam_id,
    e.exam_name,
    sub.id AS subject_id,
    sub.name AS subject_name,
    ssm.theory_marks,
    ssm.practical_marks,
    ssm.internal_marks,
    ssm.total_marks,
    ssm.max_marks,
    ssm.percentage,
    ssm.grade,
    ssm.is_pass,
    CASE 
        WHEN ss.is_compulsory = TRUE THEN 'Compulsory'
        WHEN ss.is_elective = TRUE THEN 'Elective'
        WHEN ss.is_additional = TRUE THEN 'Additional'
        WHEN ss.is_optional = TRUE THEN 'Optional'
        ELSE 'Regular'
    END AS subject_type
FROM 
    students s
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN simple_sections cs ON s.section_id = cs.id
    LEFT JOIN trades t ON s.trade_id = t.id
    LEFT JOIN streams str ON s.stream_id = str.id
    LEFT JOIN student_subjects ss ON s.id = ss.student_id AND ss.academic_session = s.academic_session
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id 
        AND sub.id = ssm.subject_id 
        AND ssm.academic_session = s.academic_session
    LEFT JOIN exams e ON ssm.exam_id = e.exam_id AND e.academic_session = s.academic_session
WHERE ssm.id IS NOT NULL;

-- Create a view for class-wise student performance
CREATE OR REPLACE VIEW class_performance AS
SELECT 
    c.id AS class_id,
    c.grade AS class_name,
    cs.name AS section_name,
    t.name AS trade_name,
    e.exam_id,
    e.exam_name,
    sub.id AS subject_id,
    sub.name AS subject_name,
    s.academic_session,
    COUNT(DISTINCT s.id) AS total_students,
    SUM(CASE WHEN ssm.is_pass = TRUE THEN 1 ELSE 0 END) AS passed_students,
    ROUND(AVG(ssm.percentage), 2) AS average_percentage,
    MAX(ssm.percentage) AS highest_percentage,
    MIN(ssm.percentage) AS lowest_percentage
FROM 
    students s
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN simple_sections cs ON s.section_id = cs.id
    LEFT JOIN trades t ON s.trade_id = t.id
    LEFT JOIN student_subjects ss ON s.id = ss.student_id AND ss.academic_session = s.academic_session
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id 
        AND sub.id = ssm.subject_id 
        AND ssm.academic_session = s.academic_session
    LEFT JOIN exams e ON ssm.exam_id = e.exam_id AND e.academic_session = s.academic_session
WHERE ssm.id IS NOT NULL
GROUP BY 
    c.id, cs.id, t.id, e.exam_id, sub.id, s.academic_session;

-- Commit the transaction
COMMIT;

-- Show a success message
SELECT 'Database structure update completed successfully!' AS message;

-- Show summary of what was created/updated
SELECT 'Summary of changes:' AS summary;
SELECT COUNT(*) AS simple_sections_count FROM simple_sections;
SELECT COUNT(*) AS subjects_count FROM subjects;
SELECT COUNT(*) AS students_with_section_id FROM students WHERE section_id IS NOT NULL;
SELECT COUNT(*) AS students_with_academic_session FROM students WHERE academic_session IS NOT NULL;
