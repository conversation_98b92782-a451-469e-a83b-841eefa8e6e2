<!-- Filters and Controls -->
<div class="exam-card p-6 mb-6">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex flex-col sm:flex-row gap-4">
            <!-- Trade Filter -->
            <div class="flex items-center">
                <label class="text-sm font-medium text-gray-700 mr-2">Trade:</label>
                <select id="tradeFilter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" <%= filters.trade === 'all' ? 'selected' : '' %>>All Trades</option>
                    <% trades.forEach(trade => { %>
                        <option value="<%= trade.trade %>" <%= filters.trade === trade.trade ? 'selected' : '' %>><%= trade.trade %></option>
                    <% }); %>
                </select>
            </div>

            <!-- Class Filter -->
            <div class="flex items-center">
                <label class="text-sm font-medium text-gray-700 mr-2">Class:</label>
                <select id="classFilter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" <%= filters.class === 'all' ? 'selected' : '' %>>All Classes</option>
                    <% classes.forEach(classItem => { %>
                        <option value="<%= classItem.class %>" <%= filters.class === classItem.class ? 'selected' : '' %>>Class <%= classItem.class %></option>
                    <% }); %>
                </select>
            </div>

            <!-- Section Filter -->
            <div class="flex items-center">
                <label class="text-sm font-medium text-gray-700 mr-2">Section:</label>
                <select id="sectionFilter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" <%= filters.section === 'all' ? 'selected' : '' %>>All Sections</option>
                    <% sections.forEach(section => { %>
                        <option value="<%= section.section %>" <%= filters.section === section.section ? 'selected' : '' %>>Section <%= section.section %></option>
                    <% }); %>
                </select>
            </div>
        </div>

        <div class="flex gap-2">
            <button onclick="applyFilters()" class="btn-exam-primary">
                <i class="fas fa-filter mr-2"></i>Apply Filters
            </button>
            <a href="/exam-results/export/excel?trade=<%= filters.trade %>&class=<%= filters.class %>&section=<%= filters.section %>" class="btn-exam-secondary">
                <i class="fas fa-file-excel mr-2"></i>Export Excel
            </a>
        </div>
    </div>
</div>

<!-- Trade-based Tabs -->
<div class="exam-card mb-6">
    <div class="exam-tabs flex border-b">
        <button class="exam-tab <%= filters.trade === 'all' ? 'active' : '' %>" onclick="switchTrade('all')">
            <i class="fas fa-users mr-2"></i>All Students
        </button>
        <% trades.forEach(trade => { %>
            <button class="exam-tab <%= filters.trade === trade.trade ? 'active' : '' %>" onclick="switchTrade('<%= trade.trade %>')">
                <i class="fas fa-graduation-cap mr-2"></i><%= trade.trade %>
            </button>
        <% }); %>
    </div>
</div>

<!-- Results Summary -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <div class="stats-card">
        <div class="text-center">
            <p class="text-2xl font-bold text-blue-600"><%= studentResults.length %></p>
            <p class="text-sm text-gray-600">Total Students</p>
        </div>
    </div>
    <div class="stats-card">
        <div class="text-center">
            <% 
                const avgPercentage = studentResults.length > 0 ? 
                    studentResults.reduce((sum, student) => sum + (student.overall_percentage || 0), 0) / studentResults.length : 0;
            %>
            <p class="text-2xl font-bold text-green-600"><%= Number(avgPercentage).toFixed(1) %>%</p>
            <p class="text-sm text-gray-600">Average Performance</p>
        </div>
    </div>
    <div class="stats-card">
        <div class="text-center">
            <% const passCount = studentResults.filter(student => (student.overall_percentage || 0) >= 33).length; %>
            <p class="text-2xl font-bold text-emerald-600"><%= passCount %></p>
            <p class="text-sm text-gray-600">Students Passed</p>
        </div>
    </div>
    <div class="stats-card">
        <div class="text-center">
            <% const excellentCount = studentResults.filter(student => (student.overall_percentage || 0) >= 90).length; %>
            <p class="text-2xl font-bold text-purple-600"><%= excellentCount %></p>
            <p class="text-sm text-gray-600">Excellent (90%+)</p>
        </div>
    </div>
</div>

<!-- Student Results Table -->
<div class="exam-card">
    <div class="overflow-x-auto">
        <table class="exam-table w-full">
            <thead>
                <tr>
                    <th class="px-4 py-3">Student ID</th>
                    <th class="px-4 py-3">Roll Number</th>
                    <th class="px-4 py-3">Student Name</th>
                    <th class="px-4 py-3">Class</th>
                    <th class="px-4 py-3">Section</th>
                    <th class="px-4 py-3">Trade</th>
                    <th class="px-4 py-3">Subjects</th>
                    <th class="px-4 py-3">Grand Total</th>
                    <th class="px-4 py-3">Overall %</th>
                    <th class="px-4 py-3">Grade</th>
                    <th class="px-4 py-3">Status</th>
                    <th class="px-4 py-3">Actions</th>
                </tr>
            </thead>
            <tbody>
                <% if (studentResults && studentResults.length > 0) { %>
                    <% studentResults.forEach(student => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-3 font-medium"><%= student.student_info.student_id %></td>
                            <td class="px-4 py-3"><%= student.student_info.roll_number %></td>
                            <td class="px-4 py-3 font-medium text-left">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3">
                                        <%= student.student_info.student_name.charAt(0).toUpperCase() %>
                                    </div>
                                    <%= student.student_info.student_name %>
                                </div>
                            </td>
                            <td class="px-4 py-3"><%= student.student_info.class %></td>
                            <td class="px-4 py-3"><%= student.student_info.section %></td>
                            <td class="px-4 py-3">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                                    <%= student.student_info.trade_full_name || student.student_info.trade %>
                                </span>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-sm">
                                    <div class="text-green-600 font-medium">Core: <%= student.subjects.filter(s => s.include_in_grand_total).length %></div>
                                    <div class="text-gray-500">Additional: <%= student.subjects.filter(s => !s.include_in_grand_total).length %></div>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-sm">
                                    <div class="font-bold"><%= Number(student.grand_total_marks || 0).toFixed(1) %> / <%= Number(student.grand_total_max || 0).toFixed(1) %></div>
                                    <div class="text-gray-500 text-xs">
                                        Additional: <%= Number(student.additional_marks || 0).toFixed(1) %> / <%= Number(student.additional_max || 0).toFixed(1) %>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-lg font-bold <%= (student.overall_percentage || 0) >= 75 ? 'text-green-600' : (student.overall_percentage || 0) >= 50 ? 'text-yellow-600' : 'text-red-600' %>">
                                    <%= Number(student.overall_percentage || 0).toFixed(1) %>%
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                    <div class="bg-gradient-to-r from-blue-600 to-green-600 h-1.5 rounded-full" style="width: <%= Math.min(student.overall_percentage || 0, 100) %>%"></div>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <span class="grade-badge <%= getGradeBadgeClass(student.overall_grade || 'F') %>">
                                    <%= student.overall_grade || 'F' %>
                                </span>
                            </td>
                            <td class="px-4 py-3">
                                <span class="px-2 py-1 rounded-full text-xs font-medium <%= (student.promotion_status === 'PROMOTED') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                                    <%= student.promotion_status || 'PENDING' %>
                                </span>
                            </td>
                            <td class="px-4 py-3">
                                <div class="flex gap-2">
                                    <button onclick="viewStudentDetails(<%= student.student_info.student_id %>)" class="text-blue-600 hover:text-blue-800" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button onclick="generateScoreCard(<%= student.student_info.student_id %>)" class="text-green-600 hover:text-green-800" title="Generate Score Card">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                <% } else { %>
                    <tr>
                        <td colspan="12" class="px-4 py-8 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-users text-4xl mb-4 text-gray-300"></i>
                                <p class="text-lg font-medium">No student results found</p>
                                <p class="text-sm">Try adjusting your filters or check if data has been loaded</p>
                            </div>
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>
</div>

<!-- Student Details Modal -->
<div id="studentDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="p-6 border-b">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-gray-800">Student Details</h2>
                    <button onclick="closeStudentModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="studentDetailsContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function getGradeBadgeClass(grade) {
    const gradeClasses = {
        'A+': 'grade-a-plus',
        'A': 'grade-a',
        'B+': 'grade-b-plus',
        'B': 'grade-b',
        'C+': 'grade-c-plus',
        'C': 'grade-c',
        'D': 'grade-d',
        'F': 'grade-f'
    };
    return gradeClasses[grade] || 'grade-f';
}

function switchTrade(trade) {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('trade', trade);
    window.location.href = currentUrl.toString();
}

function applyFilters() {
    const trade = document.getElementById('tradeFilter').value;
    const classLevel = document.getElementById('classFilter').value;
    const section = document.getElementById('sectionFilter').value;
    
    const url = new URL(window.location);
    url.searchParams.set('trade', trade);
    url.searchParams.set('class', classLevel);
    url.searchParams.set('section', section);
    
    window.location.href = url.toString();
}

function viewStudentDetails(studentId) {
    // Find student data from the current results
    const studentData = <%= JSON.stringify(studentResults) %>.find(s => s.student_info.student_id == studentId);
    
    if (!studentData) {
        alert('Student data not found');
        return;
    }

    const content = `
        <div class="space-y-6">
            <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white p-4 rounded-lg">
                <h3 class="text-lg font-bold">${studentData.student_info.student_name}</h3>
                <p>Student ID: ${studentData.student_info.student_id} | Roll: ${studentData.student_info.roll_number}</p>
                <p>Class: ${studentData.student_info.class}-${studentData.student_info.section} | Trade: ${studentData.student_info.trade_full_name || studentData.student_info.trade}</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-green-50 p-4 rounded-lg text-center">
                    <p class="text-2xl font-bold text-green-600">${Number(studentData.overall_percentage || 0).toFixed(1)}%</p>
                    <p class="text-sm text-gray-600">Overall Percentage</p>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg text-center">
                    <p class="text-2xl font-bold text-blue-600">${studentData.overall_grade}</p>
                    <p class="text-sm text-gray-600">Overall Grade</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg text-center">
                    <p class="text-2xl font-bold text-purple-600">${studentData.promotion_status}</p>
                    <p class="text-sm text-gray-600">Status</p>
                </div>
            </div>
            
            <div>
                <h4 class="text-lg font-bold mb-4">Subject-wise Performance</h4>
                <div class="overflow-x-auto">
                    <table class="w-full border border-gray-200 rounded-lg">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left">Subject</th>
                                <th class="px-4 py-2 text-center">Category</th>
                                <th class="px-4 py-2 text-center">Theory</th>
                                <th class="px-4 py-2 text-center">Practical</th>
                                <th class="px-4 py-2 text-center">CCE</th>
                                <th class="px-4 py-2 text-center">Total</th>
                                <th class="px-4 py-2 text-center">%</th>
                                <th class="px-4 py-2 text-center">Grade</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${studentData.subjects.map(subject => `
                                <tr class="border-t">
                                    <td class="px-4 py-2 font-medium">${subject.subject_name}</td>
                                    <td class="px-4 py-2 text-center">
                                        <span class="px-2 py-1 rounded text-xs ${subject.include_in_grand_total ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                            ${subject.include_in_grand_total ? 'Core' : 'Additional'}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 text-center">${Number(subject.theory_marks || 0).toFixed(1)}</td>
                                    <td class="px-4 py-2 text-center">${Number(subject.practical_marks || 0).toFixed(1)}</td>
                                    <td class="px-4 py-2 text-center">${Number(subject.internal_marks || 0).toFixed(1)}</td>
                                    <td class="px-4 py-2 text-center font-bold">${Number(subject.total_marks || 0).toFixed(1)}</td>
                                    <td class="px-4 py-2 text-center">${Number(subject.percentage || 0).toFixed(1)}%</td>
                                    <td class="px-4 py-2 text-center">
                                        <span class="grade-badge ${getGradeBadgeClass(subject.grade)}">${subject.grade}</span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('studentDetailsContent').innerHTML = content;
    document.getElementById('studentDetailsModal').classList.remove('hidden');
}

function closeStudentModal() {
    document.getElementById('studentDetailsModal').classList.add('hidden');
}

function generateScoreCard(studentId) {
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;
    
    button.innerHTML = '<div class="loading-spinner"></div>';
    button.disabled = true;
    
    fetch(`/exam-results/student/${studentId}/scorecard`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.open(data.url, '_blank');
                showToast('Score card generated successfully!', 'success');
            } else {
                showToast('Error generating score card: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Error generating score card', 'error');
        })
        .finally(() => {
            button.innerHTML = originalContent;
            button.disabled = false;
        });
}

function showToast(message, type) {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Initialize grade badges
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.grade-badge').forEach(badge => {
        const grade = badge.textContent.trim();
        badge.classList.add(getGradeBadgeClass(grade));
    });
});
</script>
