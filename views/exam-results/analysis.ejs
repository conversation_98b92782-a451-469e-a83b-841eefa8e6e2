<!-- Analysis Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Total Classes</p>
                <p class="text-3xl font-bold text-gray-800"><%= classAnalysis.length %></p>
                <p class="text-blue-600 text-sm mt-1">
                    <i class="fas fa-layer-group mr-1"></i>
                    Active Levels
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-school"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Academic Trades</p>
                <p class="text-3xl font-bold text-gray-800"><%= tradeAnalysis.length %></p>
                <p class="text-green-600 text-sm mt-1">
                    <i class="fas fa-stream mr-1"></i>
                    Specializations
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-sitemap"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Class Sections</p>
                <p class="text-3xl font-bold text-gray-800"><%= sectionAnalysis.length %></p>
                <p class="text-purple-600 text-sm mt-1">
                    <i class="fas fa-users mr-1"></i>
                    Total Sections
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-th-large"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Top Performers</p>
                <p class="text-3xl font-bold text-gray-800"><%= topPerformers.length %></p>
                <p class="text-orange-600 text-sm mt-1">
                    <i class="fas fa-trophy mr-1"></i>
                    Excellence
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-medal"></i>
            </div>
        </div>
    </div>
</div>

<!-- Performance Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Trade-wise Performance Chart -->
    <div class="exam-card p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-chart-bar text-green-600 mr-2"></i>
            Trade-wise Performance
        </h2>
        <div class="h-64">
            <canvas id="tradeChart"></canvas>
        </div>
    </div>

    <!-- Class-wise Performance Chart -->
    <div class="exam-card p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-chart-line text-blue-600 mr-2"></i>
            Class-wise Performance
        </h2>
        <div class="h-64">
            <canvas id="classChart"></canvas>
        </div>
    </div>
</div>

<!-- Detailed Analysis Tables -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Trade-wise Analysis Table -->
    <div class="exam-card">
        <div class="p-6 border-b">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-graduation-cap text-green-600 mr-2"></i>
                Trade-wise Analysis
            </h2>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gradient-to-r from-green-600 to-blue-600 text-white">
                    <tr>
                        <th class="px-4 py-3 text-left">Trade</th>
                        <th class="px-4 py-3 text-center">Students</th>
                        <th class="px-4 py-3 text-center">Avg %</th>
                        <th class="px-4 py-3 text-center">Highest</th>
                        <th class="px-4 py-3 text-center">Lowest</th>
                        <th class="px-4 py-3 text-center">Pass Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <% tradeAnalysis.forEach((trade, index) => { %>
                        <tr class="<%= index % 2 === 0 ? 'bg-gray-50' : 'bg-white' %> hover:bg-blue-50">
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-3 bg-gradient-to-r from-green-600 to-blue-600"></div>
                                    <div>
                                        <p class="font-semibold text-gray-800"><%= trade.trade_full_name || trade.trade %></p>
                                        <p class="text-xs text-gray-500"><%= trade.trade %></p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3 text-center font-medium"><%= trade.student_count %></td>
                            <td class="px-4 py-3 text-center">
                                <span class="font-bold text-lg <%= (trade.average_percentage || 0) >= 75 ? 'text-green-600' : (trade.average_percentage || 0) >= 50 ? 'text-yellow-600' : 'text-red-600' %>">
                                    <%= (trade.average_percentage || 0).toFixed(1) %>%
                                </span>
                            </td>
                            <td class="px-4 py-3 text-center text-green-600 font-medium"><%= (trade.highest_percentage || 0).toFixed(1) %>%</td>
                            <td class="px-4 py-3 text-center text-red-600 font-medium"><%= (trade.lowest_percentage || 0).toFixed(1) %>%</td>
                            <td class="px-4 py-3 text-center">
                                <% const passRate = trade.student_count > 0 ? ((trade.pass_count || 0) / trade.student_count * 100) : 0; %>
                                <div class="flex items-center justify-center">
                                    <span class="font-medium <%= passRate >= 80 ? 'text-green-600' : passRate >= 60 ? 'text-yellow-600' : 'text-red-600' %>">
                                        <%= passRate.toFixed(1) %>%
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                    <div class="bg-gradient-to-r from-green-600 to-blue-600 h-1.5 rounded-full" style="width: <%= passRate %>%"></div>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Class-wise Analysis Table -->
    <div class="exam-card">
        <div class="p-6 border-b">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-layer-group text-blue-600 mr-2"></i>
                Class-wise Analysis
            </h2>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gradient-to-r from-blue-600 to-green-600 text-white">
                    <tr>
                        <th class="px-4 py-3 text-left">Class</th>
                        <th class="px-4 py-3 text-center">Students</th>
                        <th class="px-4 py-3 text-center">Avg %</th>
                        <th class="px-4 py-3 text-center">Highest</th>
                        <th class="px-4 py-3 text-center">Lowest</th>
                        <th class="px-4 py-3 text-center">Pass Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <% classAnalysis.forEach((classData, index) => { %>
                        <tr class="<%= index % 2 === 0 ? 'bg-gray-50' : 'bg-white' %> hover:bg-blue-50">
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-green-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                                        <%= classData.class %>
                                    </div>
                                    <span class="font-semibold text-gray-800">Class <%= classData.class %></span>
                                </div>
                            </td>
                            <td class="px-4 py-3 text-center font-medium"><%= classData.student_count %></td>
                            <td class="px-4 py-3 text-center">
                                <span class="font-bold text-lg <%= (classData.average_percentage || 0) >= 75 ? 'text-green-600' : (classData.average_percentage || 0) >= 50 ? 'text-yellow-600' : 'text-red-600' %>">
                                    <%= (classData.average_percentage || 0).toFixed(1) %>%
                                </span>
                            </td>
                            <td class="px-4 py-3 text-center text-green-600 font-medium"><%= (classData.highest_percentage || 0).toFixed(1) %>%</td>
                            <td class="px-4 py-3 text-center text-red-600 font-medium"><%= (classData.lowest_percentage || 0).toFixed(1) %>%</td>
                            <td class="px-4 py-3 text-center">
                                <% const passRate = classData.student_count > 0 ? ((classData.pass_count || 0) / classData.student_count * 100) : 0; %>
                                <div class="flex items-center justify-center">
                                    <span class="font-medium <%= passRate >= 80 ? 'text-green-600' : passRate >= 60 ? 'text-yellow-600' : 'text-red-600' %>">
                                        <%= passRate.toFixed(1) %>%
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                    <div class="bg-gradient-to-r from-blue-600 to-green-600 h-1.5 rounded-full" style="width: <%= passRate %>%"></div>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Section-wise Performance and Top Performers -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Section-wise Performance -->
    <div class="exam-card">
        <div class="p-6 border-b">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-th-large text-purple-600 mr-2"></i>
                Section-wise Performance
            </h2>
        </div>
        <div class="p-6">
            <div class="space-y-4 max-h-96 overflow-y-auto">
                <% sectionAnalysis.forEach(section => { %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">
                                <%= section.section %>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-800"><%= section.class_section %></p>
                                <p class="text-sm text-gray-600"><%= section.student_count %> students</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-lg <%= (section.average_percentage || 0) >= 75 ? 'text-green-600' : (section.average_percentage || 0) >= 50 ? 'text-yellow-600' : 'text-red-600' %>">
                                <%= (section.average_percentage || 0).toFixed(1) %>%
                            </p>
                            <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                                <div class="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full" style="width: <%= Math.min(section.average_percentage || 0, 100) %>%"></div>
                            </div>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>

    <!-- Top Performers -->
    <div class="exam-card">
        <div class="p-6 border-b">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-trophy text-yellow-600 mr-2"></i>
                Top Performers
            </h2>
        </div>
        <div class="p-6">
            <div class="space-y-4 max-h-96 overflow-y-auto">
                <% topPerformers.forEach((student, index) => { %>
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                                <%= index + 1 %>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-800"><%= student.name %></p>
                                <p class="text-sm text-gray-600">
                                    <%= student.roll_number %> | <%= student.class %>-<%= student.section %> | <%= student.trade_full_name || student.trade %>
                                </p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-xl text-green-600"><%= (student.average_percentage || 0).toFixed(1) %>%</p>
                            <% if (index === 0) { %>
                                <i class="fas fa-crown text-yellow-500"></i>
                            <% } else if (index === 1) { %>
                                <i class="fas fa-medal text-gray-400"></i>
                            <% } else if (index === 2) { %>
                                <i class="fas fa-medal text-yellow-600"></i>
                            <% } %>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>
</div>

<!-- Performance Insights -->
<div class="exam-card p-6">
    <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
        <i class="fas fa-lightbulb text-yellow-600 mr-2"></i>
        Performance Insights
    </h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-center mb-2">
                <i class="fas fa-arrow-up text-green-600 mr-2"></i>
                <h3 class="font-semibold text-green-800">Best Performing Trade</h3>
            </div>
            <% if (tradeAnalysis.length > 0) { %>
                <p class="text-lg font-bold text-green-600"><%= tradeAnalysis[0].trade_full_name || tradeAnalysis[0].trade %></p>
                <p class="text-sm text-green-700"><%= (tradeAnalysis[0].average_percentage || 0).toFixed(1) %>% average</p>
            <% } else { %>
                <p class="text-gray-500">No data available</p>
            <% } %>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center mb-2">
                <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                <h3 class="font-semibold text-blue-800">Best Performing Class</h3>
            </div>
            <% if (classAnalysis.length > 0) { %>
                <% const bestClass = classAnalysis.reduce((best, current) => (current.average_percentage || 0) > (best.average_percentage || 0) ? current : best); %>
                <p class="text-lg font-bold text-blue-600">Class <%= bestClass.class %></p>
                <p class="text-sm text-blue-700"><%= (bestClass.average_percentage || 0).toFixed(1) %>% average</p>
            <% } else { %>
                <p class="text-gray-500">No data available</p>
            <% } %>
        </div>

        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div class="flex items-center mb-2">
                <i class="fas fa-star text-purple-600 mr-2"></i>
                <h3 class="font-semibold text-purple-800">Overall Excellence</h3>
            </div>
            <% 
                const totalStudents = topPerformers.length;
                const excellentStudents = topPerformers.filter(s => (s.average_percentage || 0) >= 90).length;
                const excellenceRate = totalStudents > 0 ? (excellentStudents / totalStudents * 100) : 0;
            %>
            <p class="text-lg font-bold text-purple-600"><%= excellentStudents %> students</p>
            <p class="text-sm text-purple-700">90%+ performance</p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Trade-wise Performance Chart
    const tradeCtx = document.getElementById('tradeChart').getContext('2d');
    const tradeData = <%= JSON.stringify(tradeAnalysis) %>;
    
    new Chart(tradeCtx, {
        type: 'doughnut',
        data: {
            labels: tradeData.map(t => t.trade_full_name || t.trade),
            datasets: [{
                data: tradeData.map(t => t.average_percentage || 0),
                backgroundColor: [
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(168, 85, 247, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ],
                borderColor: [
                    'rgba(34, 197, 94, 1)',
                    'rgba(59, 130, 246, 1)',
                    'rgba(168, 85, 247, 1)',
                    'rgba(245, 158, 11, 1)',
                    'rgba(239, 68, 68, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed.toFixed(1) + '%';
                        }
                    }
                }
            }
        }
    });

    // Class-wise Performance Chart
    const classCtx = document.getElementById('classChart').getContext('2d');
    const classData = <%= JSON.stringify(classAnalysis) %>;
    
    new Chart(classCtx, {
        type: 'line',
        data: {
            labels: classData.map(c => `Class ${c.class}`),
            datasets: [{
                label: 'Average Percentage',
                data: classData.map(c => c.average_percentage || 0),
                borderColor: 'rgba(59, 130, 246, 1)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                pointBorderColor: 'white',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(59, 130, 246, 0.8)',
                    borderWidth: 1,
                    cornerRadius: 8
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
});
</script>
