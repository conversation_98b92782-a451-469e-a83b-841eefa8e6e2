<!DOCTYPE html>
<html lang="<%= typeof currentLanguage !== 'undefined' ? currentLanguage : 'en' %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - Exam Prep Platform</title>
  <link rel="stylesheet" href="/styles.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <link rel="stylesheet" href="/css/app.css">
  <link rel="stylesheet" href="/css/site-title.css">
  <link rel="stylesheet" href="/css/spacing-utilities.css">
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="/js/app.js"></script>
  <style>
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #e9d5ff;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a855f7;
    }

    /* Background overlay */
    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.4));
      z-index: -1;
    }

    /* Form container animation */
    .auth-container {
      animation: fadeIn 0.8s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }
  </style>
</head>
<body class="min-h-screen flex flex-col items-center justify-center bg-cover bg-center relative" style="background-image: url('/images/examprepgu.png');">
  <!-- Language Switcher in Top Right Corner -->
  <div class="absolute top-4 right-4 z-10">
    <div class="language-switcher-auth">
      <div class="relative inline-block text-left">
        <button type="button" class="flex items-center text-white bg-black/30 hover:bg-black/40 px-3 py-1.5 rounded-md transition" id="language-menu-button-auth" aria-expanded="false" aria-haspopup="true">
          <% if (typeof currentLanguage !== 'undefined') { %>
            <% if (currentLanguage === 'en') { %>
              <span class="mr-1">English</span>
            <% } else if (currentLanguage === 'pa') { %>
              <span class="mr-1">ਪੰਜਾਬੀ</span>
            <% } %>
          <% } else { %>
            <span class="mr-1">English</span>
          <% } %>
          <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
        <div class="language-dropdown-auth hidden origin-top-right absolute right-0 mt-2 w-36 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50" role="menu" aria-orientation="vertical" aria-labelledby="language-menu-button-auth" tabindex="-1">
          <div class="py-1" role="none">
            <a href="/language/switch/en" class="<%= typeof currentLanguage !== 'undefined' && currentLanguage === 'en' ? 'bg-gray-100 text-gray-900' : 'text-gray-700' %> block px-4 py-2 text-sm hover:bg-gray-100" role="menuitem" tabindex="-1">English</a>
            <a href="/language/switch/pa" class="<%= typeof currentLanguage !== 'undefined' && currentLanguage === 'pa' ? 'bg-gray-100 text-gray-900' : 'text-gray-700' %> block px-4 py-2 text-sm hover:bg-gray-100" role="menuitem" tabindex="-1">ਪੰਜਾਬੀ</a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="mb-8 text-center">
    <!-- Site Logo -->
    <% if (locals.siteSettings && siteSettings.site_logo) { %>
      <div class="flex justify-center mb-4">
        <img src="<%= siteSettings.site_logo %>" alt="Exam Prep Platform" class="h-20 w-20 object-cover rounded-md border-2 border-white/50 shadow-lg">
      </div>
    <% } %>
    <h1 class="text-3xl font-bold text-white drop-shadow-lg">Exam Prep Platform</h1>
    <p class="text-white text-xl mt-2 drop-shadow-md">Senior Secondary Residential School for Meritorious Students, Ludhiana</p>
  </div>

<div class="bg-white/90 backdrop-blur-sm p-8 rounded-lg shadow-lg w-full max-w-4xl border border-white/20 auth-container">
    <h1 class="text-2xl font-bold text-center mb-4 text-gray-800">Demo Login</h1>
    <p class="text-center text-gray-600 mb-8">Select a role to login with pre-configured demo credentials</p>

    <% if (error) { %>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline"><%= error %></span>
        </div>
    <% } %>

    <!-- Debug info (only visible in development) -->
    <% if (process.env.NODE_ENV === 'development') { %>
        <div class="mb-4 p-3 bg-gray-100 rounded-md text-xs">
            <p>Number of demo accounts: <%= demoAccounts ? demoAccounts.length : 0 %></p>
            <p>Roles: <%= demoAccounts ? demoAccounts.map(a => a.role).join(', ') : 'none' %></p>
        </div>
    <% } %>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% if (demoAccounts && demoAccounts.length > 0) { %>
            <% demoAccounts.forEach(account => { %>
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow duration-300">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center <%= account.color === 'navy' ? 'bg-blue-900 text-yellow-400' : account.color === 'teal' ? 'bg-teal-100 text-teal-600' : 'bg-' + account.color + '-100 text-' + account.color + '-600' %>">
                                <i class="fas <%= account.icon %> text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-800"><%= account.title %></h3>
                                <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full <%= account.color === 'navy' ? 'bg-blue-900 text-yellow-400' : account.color === 'teal' ? 'bg-teal-100 text-teal-800' : 'bg-' + account.color + '-100 text-' + account.color + '-800' %>">
                                    <%= account.role %>
                                </span>
                            </div>
                        </div>

                        <p class="text-gray-600 mb-4"><%= account.description %></p>

                        <div class="mb-4 bg-gray-50 p-3 rounded-md">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-500">Username/Email:</span>
                                <span class="text-sm font-mono bg-gray-100 px-2 py-1 rounded"><%= account.username %></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500">Password:</span>
                                <span class="text-sm font-mono bg-gray-100 px-2 py-1 rounded"><%= account.password %></span>
                            </div>
                        </div>

                        <form action="/demo-login" method="POST">
                            <input type="hidden" name="role" value="<%= account.role %>">
                            <button type="submit" class="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white <%= account.color === 'navy' ? 'bg-blue-900 hover:bg-blue-800 focus:ring-blue-500' : account.color === 'teal' ? 'bg-teal-600 hover:bg-teal-700 focus:ring-teal-500' : 'bg-' + account.color + '-600 hover:bg-' + account.color + '-700 focus:ring-' + account.color + '-500' %> focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200">
                                Login as <%= account.title %>
                            </button>
                        </form>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="col-span-full p-4 bg-yellow-100 text-yellow-800 rounded-md">
                No demo accounts available. Please check the server configuration.
            </div>
        <% } %>
    </div>

    <div class="mt-8 text-center">
        <p class="text-sm text-gray-600">
            Want to use your own account?
            <a href="/login" class="font-medium text-indigo-600 hover:text-indigo-500">
                Go to regular login
            </a>
        </p>
    </div>
</div>

<!-- Language Switcher Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add FontAwesome if not already loaded
        if (!document.querySelector('link[href*="font-awesome"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
            document.head.appendChild(link);
        }

        // Language dropdown toggle for auth pages
        const languageMenuButtonAuth = document.getElementById('language-menu-button-auth');
        const languageDropdownAuth = document.querySelector('.language-dropdown-auth');

        if (languageMenuButtonAuth && languageDropdownAuth) {
            languageMenuButtonAuth.addEventListener('click', function(e) {
                e.preventDefault();
                languageDropdownAuth.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!languageMenuButtonAuth.contains(event.target) && !languageDropdownAuth.contains(event.target)) {
                    languageDropdownAuth.classList.add('hidden');
                }
            });
        }
    });
</script>
</body>
</html>
