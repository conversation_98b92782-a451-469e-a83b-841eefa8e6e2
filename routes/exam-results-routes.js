const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAuthenticated } = require('../middleware/auth');
const PDFDocument = require('pdfkit');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

// Demo login for exam results view
router.get('/login', (req, res) => {
    res.render('exam-results/login', { 
        title: 'Exam Results Login',
        layout: false,
        error: req.session.flashError,
        success: req.session.flashSuccess
    });
});

// Handle demo login
router.post('/login', (req, res) => {
    const { username, password } = req.body;
    
    // Demo credentials for exam results access
    if (username === 'examresults' && password === 'results2024') {
        req.session.examResultsAccess = true;
        req.session.examResultsUser = 'Exam Results Coordinator';
        req.session.flashSuccess = 'Successfully logged in to Exam Results System';
        return res.redirect('/exam-results/dashboard');
    }
    
    req.session.flashError = 'Invalid credentials. Use: examresults / results2024';
    res.redirect('/exam-results/login');
});

// Middleware to check exam results access
const checkExamResultsAccess = (req, res, next) => {
    if (!req.session.examResultsAccess) {
        return res.redirect('/exam-results/login');
    }
    next();
};

// Main dashboard
router.get('/dashboard', checkExamResultsAccess, async (req, res) => {
    try {
        // Get overall statistics
        const [stats] = await db.execute(`
            SELECT 
                COUNT(DISTINCT s.id) as total_students,
                COUNT(DISTINCT s.class) as total_classes,
                COUNT(DISTINCT s.trade) as total_trades,
                COUNT(DISTINCT ssm.exam_id) as total_exams,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as overall_average
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            WHERE s.academic_session = '2023-2024'
        `);

        // Get trade-wise statistics
        const [tradeStats] = await db.execute(`
            SELECT 
                s.trade,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            WHERE s.academic_session = '2023-2024'
            GROUP BY s.trade
            ORDER BY average_percentage DESC
        `);

        // Get class-wise statistics
        const [classStats] = await db.execute(`
            SELECT 
                s.class,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            WHERE s.academic_session = '2023-2024'
            GROUP BY s.class
            ORDER BY s.class
        `);

        res.render('exam-results/dashboard', {
            title: 'Exam Results Dashboard',
            layout: 'layouts/exam-results',
            currentPage: 'dashboard',
            stats: stats[0] || {},
            tradeStats,
            classStats,
            user: req.session.examResultsUser
        });
    } catch (error) {
        console.error('Error loading exam results dashboard:', error);
        req.session.flashError = 'Error loading dashboard data';
        res.redirect('/exam-results/login');
    }
});

// Student results view with trade-based tabs
router.get('/students', checkExamResultsAccess, async (req, res) => {
    try {
        const { trade = 'all', class: classLevel = 'all', section = 'all' } = req.query;

        let whereConditions = ["s.academic_session = '2023-2024'"];
        let queryParams = [];

        if (trade !== 'all') {
            whereConditions.push('s.trade = ?');
            queryParams.push(trade);
        }
        if (classLevel !== 'all') {
            whereConditions.push('s.class = ?');
            queryParams.push(classLevel);
        }
        if (section !== 'all') {
            whereConditions.push('s.section = ?');
            queryParams.push(section);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get student results with subject-wise marks
        const [studentResults] = await db.execute(`
            SELECT 
                s.id as student_id,
                s.student_id as roll_number,
                s.name as student_name,
                s.class,
                s.section,
                s.trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name,
                
                -- Subject information
                sub.code as subject_code,
                sub.name as subject_name,
                sub.subject_category_new,
                
                -- Subject classification for trade-specific logic
                CASE 
                    WHEN sub.code IN ('1', '2') THEN 'Compulsory Language'
                    WHEN sub.code IN ('28', '52', '53') AND s.trade = 'Non-Medical' THEN 'Core Compulsory'
                    WHEN sub.code IN ('141', '142', '26') AND s.trade = 'Commerce' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND s.trade = 'Medical' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND s.trade = 'Non-Medical' THEN 'Additional Optional'
                    WHEN sub.code IN ('146', '49') THEN 'Additional Compulsory'
                    ELSE 'Other'
                END as subject_classification,
                
                -- Include in grand total flag
                CASE 
                    WHEN sub.code IN ('1', '2') THEN TRUE
                    WHEN sub.code IN ('28', '52', '53') AND s.trade = 'Non-Medical' THEN TRUE
                    WHEN sub.code IN ('141', '142', '26') AND s.trade = 'Commerce' THEN TRUE
                    WHEN sub.code = '54' AND s.trade = 'Medical' THEN TRUE
                    ELSE FALSE
                END as include_in_grand_total,
                
                -- Marks information
                ssm.theory_marks,
                ssm.practical_marks,
                ssm.internal_marks,
                ssm.total_marks,
                ssm.max_marks,
                ROUND((ssm.total_marks / ssm.max_marks) * 100, 2) as percentage,
                
                -- Grade calculation
                CASE 
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 90 THEN 'A+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 80 THEN 'A'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 70 THEN 'B+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 60 THEN 'B'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 50 THEN 'C+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 40 THEN 'C'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'D'
                    ELSE 'F'
                END as grade,
                
                -- Result status
                CASE 
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'PASS' 
                    ELSE 'FAIL' 
                END as result_status
                
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            LEFT JOIN subjects sub ON ssm.subject_id = sub.id
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE ${whereClause}
            ORDER BY s.class, s.section, s.name, 
                     CASE 
                         WHEN sub.code IN ('1', '2') THEN 1
                         WHEN sub.subject_category_new = 'science' OR sub.subject_category_new = 'commerce' THEN 2
                         ELSE 3
                     END,
                     CAST(sub.code AS UNSIGNED)
        `, queryParams);

        // Get available filters
        const [trades] = await db.execute(`
            SELECT DISTINCT trade FROM students WHERE academic_session = '2023-2024' ORDER BY trade
        `);
        
        const [classes] = await db.execute(`
            SELECT DISTINCT class FROM students WHERE academic_session = '2023-2024' ORDER BY class
        `);
        
        const [sections] = await db.execute(`
            SELECT DISTINCT section FROM students WHERE academic_session = '2023-2024' ORDER BY section
        `);

        // Group results by student
        const groupedResults = {};
        studentResults.forEach(result => {
            const studentKey = result.student_id;
            if (!groupedResults[studentKey]) {
                groupedResults[studentKey] = {
                    student_info: {
                        student_id: result.student_id,
                        roll_number: result.roll_number,
                        student_name: result.student_name,
                        class: result.class,
                        section: result.section,
                        trade: result.trade,
                        trade_full_name: result.trade_full_name
                    },
                    subjects: [],
                    grand_total_marks: 0,
                    grand_total_max: 0,
                    additional_marks: 0,
                    additional_max: 0
                };
            }
            
            if (result.subject_name) {
                groupedResults[studentKey].subjects.push(result);
                
                // Add to grand total if it's a core subject
                if (result.include_in_grand_total) {
                    groupedResults[studentKey].grand_total_marks += result.total_marks || 0;
                    groupedResults[studentKey].grand_total_max += result.max_marks || 0;
                } else {
                    groupedResults[studentKey].additional_marks += result.total_marks || 0;
                    groupedResults[studentKey].additional_max += result.max_marks || 0;
                }
            }
        });

        // Calculate overall percentages and grades
        Object.keys(groupedResults).forEach(studentKey => {
            const student = groupedResults[studentKey];
            if (student.grand_total_max > 0) {
                student.overall_percentage = Math.round((student.grand_total_marks / student.grand_total_max) * 100 * 100) / 100;
                
                // Calculate overall grade
                if (student.overall_percentage >= 90) student.overall_grade = 'A+';
                else if (student.overall_percentage >= 80) student.overall_grade = 'A';
                else if (student.overall_percentage >= 70) student.overall_grade = 'B+';
                else if (student.overall_percentage >= 60) student.overall_grade = 'B';
                else if (student.overall_percentage >= 50) student.overall_grade = 'C+';
                else if (student.overall_percentage >= 40) student.overall_grade = 'C';
                else if (student.overall_percentage >= 33) student.overall_grade = 'D';
                else student.overall_grade = 'F';
                
                student.promotion_status = student.overall_percentage >= 33 ? 'PROMOTED' : 'DETAINED';
            }
        });

        res.render('exam-results/students', {
            title: 'Student Results',
            layout: 'layouts/exam-results',
            currentPage: 'students',
            studentResults: Object.values(groupedResults),
            filters: { trade, class: classLevel, section },
            trades,
            classes,
            sections,
            user: req.session.examResultsUser
        });
    } catch (error) {
        console.error('Error loading student results:', error);
        req.session.flashError = 'Error loading student results';
        res.redirect('/exam-results/dashboard');
    }
});

// Generate detailed score card PDF
router.get('/student/:studentId/scorecard', checkExamResultsAccess, async (req, res) => {
    try {
        const studentId = req.params.studentId;

        // Get complete student information
        const [studentInfo] = await db.execute(`
            SELECT
                s.*,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name
            FROM students s
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.id = ? AND s.academic_session = '2023-2024'
        `, [studentId]);

        if (studentInfo.length === 0) {
            return res.status(404).json({ error: 'Student not found' });
        }

        const student = studentInfo[0];

        // Get subject-wise marks with categorization
        const [subjectMarks] = await db.execute(`
            SELECT
                sub.code as subject_code,
                sub.name as subject_name,
                sub.subject_category_new,

                -- Subject classification
                CASE
                    WHEN sub.code IN ('1', '2') THEN 'Compulsory Language'
                    WHEN sub.code IN ('28', '52', '53') AND ? = 'Non-Medical' THEN 'Core Compulsory'
                    WHEN sub.code IN ('141', '142', '26') AND ? = 'Commerce' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND ? = 'Medical' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND ? = 'Non-Medical' THEN 'Additional Optional'
                    WHEN sub.code IN ('146', '49') THEN 'Additional Compulsory'
                    ELSE 'Other'
                END as subject_classification,

                -- Include in grand total flag
                CASE
                    WHEN sub.code IN ('1', '2') THEN TRUE
                    WHEN sub.code IN ('28', '52', '53') AND ? = 'Non-Medical' THEN TRUE
                    WHEN sub.code IN ('141', '142', '26') AND ? = 'Commerce' THEN TRUE
                    WHEN sub.code = '54' AND ? = 'Medical' THEN TRUE
                    ELSE FALSE
                END as include_in_grand_total,

                ssm.theory_marks,
                ssm.practical_marks,
                ssm.internal_marks,
                ssm.total_marks,
                ssm.max_marks,
                ROUND((ssm.total_marks / ssm.max_marks) * 100, 2) as percentage,

                -- Grade calculation
                CASE
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 90 THEN 'A+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 80 THEN 'A'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 70 THEN 'B+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 60 THEN 'B'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 50 THEN 'C+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 40 THEN 'C'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'D'
                    ELSE 'F'
                END as grade,

                CASE
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'PASS'
                    ELSE 'FAIL'
                END as result_status

            FROM student_subject_marks ssm
            JOIN subjects sub ON ssm.subject_id = sub.id
            WHERE ssm.student_id = ? AND ssm.academic_session = '2023-2024'
            ORDER BY
                CASE
                    WHEN sub.code IN ('1', '2') THEN 1
                    WHEN sub.subject_category_new IN ('science', 'commerce') THEN 2
                    ELSE 3
                END,
                CAST(sub.code AS UNSIGNED)
        `, [student.trade, student.trade, student.trade, student.trade, student.trade, student.trade, student.trade, studentId]);

        // Calculate grand totals
        let grandTotalMarks = 0;
        let grandTotalMax = 0;
        let additionalMarks = 0;
        let additionalMax = 0;

        subjectMarks.forEach(subject => {
            if (subject.include_in_grand_total) {
                grandTotalMarks += subject.total_marks || 0;
                grandTotalMax += subject.max_marks || 0;
            } else {
                additionalMarks += subject.total_marks || 0;
                additionalMax += subject.max_marks || 0;
            }
        });

        const overallPercentage = grandTotalMax > 0 ? Math.round((grandTotalMarks / grandTotalMax) * 100 * 100) / 100 : 0;

        let overallGrade = 'F';
        if (overallPercentage >= 90) overallGrade = 'A+';
        else if (overallPercentage >= 80) overallGrade = 'A';
        else if (overallPercentage >= 70) overallGrade = 'B+';
        else if (overallPercentage >= 60) overallGrade = 'B';
        else if (overallPercentage >= 50) overallGrade = 'C+';
        else if (overallPercentage >= 40) overallGrade = 'C';
        else if (overallPercentage >= 33) overallGrade = 'D';

        const promotionStatus = overallPercentage >= 33 ? 'PROMOTED' : 'DETAINED';

        // Create PDF
        const doc = new PDFDocument({ margin: 40, size: 'A4' });
        const filename = `scorecard_${student.student_id}_${Date.now()}.pdf`;
        const outputPath = path.join(__dirname, '../public/temp', filename);

        // Ensure temp directory exists
        const tempDir = path.dirname(outputPath);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        const writeStream = fs.createWriteStream(outputPath);
        doc.pipe(writeStream);

        // Header
        doc.fontSize(20).fillColor('#2563eb').text('DETAILED SCORE CARD', { align: 'center' });
        doc.moveDown(0.5);
        doc.fontSize(12).fillColor('#6b7280').text('Academic Session 2023-2024', { align: 'center' });
        doc.moveDown(1);

        // Student Information
        doc.fontSize(14).fillColor('#1f2937').text('STUDENT INFORMATION', { underline: true });
        doc.moveDown(0.5);

        const studentInfoY = doc.y;
        doc.fontSize(10);
        doc.text(`Student ID: ${student.student_id}`, 40, studentInfoY);
        doc.text(`Roll Number: ${student.student_id}`, 200, studentInfoY);
        doc.text(`Class: ${student.class}-${student.section}`, 350, studentInfoY);

        doc.text(`Name: ${student.name}`, 40, studentInfoY + 15);
        doc.text(`Father's Name: ${student.father_name || 'N/A'}`, 200, studentInfoY + 15);
        doc.text(`Trade: ${student.trade_full_name}`, 350, studentInfoY + 15);

        doc.moveDown(2);

        // Subject-wise Performance
        doc.fontSize(14).fillColor('#1f2937').text('SUBJECT-WISE PERFORMANCE', { underline: true });
        doc.moveDown(0.5);

        // Table headers
        const tableTop = doc.y;
        const tableHeaders = ['Subject', 'Category', 'Theory', 'Practical', 'CCE', 'Total', '%', 'Grade', 'Status'];
        const colWidths = [120, 80, 40, 40, 30, 40, 35, 35, 45];
        let currentX = 40;

        doc.fontSize(8).fillColor('#374151');
        tableHeaders.forEach((header, i) => {
            doc.text(header, currentX, tableTop, { width: colWidths[i], align: 'center' });
            currentX += colWidths[i];
        });

        // Draw header line
        doc.moveTo(40, tableTop + 12).lineTo(525, tableTop + 12).stroke();

        let currentY = tableTop + 20;
        let currentCategory = '';

        subjectMarks.forEach((subject, index) => {
            // Add category separator
            if (subject.subject_classification !== currentCategory) {
                currentCategory = subject.subject_classification;
                doc.fontSize(9).fillColor('#059669').text(currentCategory.toUpperCase(), 40, currentY, { underline: true });
                currentY += 15;
            }

            currentX = 40;
            doc.fontSize(8).fillColor('#1f2937');

            const rowData = [
                subject.subject_name,
                subject.include_in_grand_total ? 'Core' : 'Additional',
                subject.theory_marks?.toFixed(1) || '0.0',
                subject.practical_marks?.toFixed(1) || '0.0',
                subject.internal_marks?.toFixed(1) || '0.0',
                subject.total_marks?.toFixed(1) || '0.0',
                subject.percentage?.toFixed(1) || '0.0',
                subject.grade || 'F',
                subject.result_status || 'FAIL'
            ];

            rowData.forEach((data, i) => {
                const align = i === 0 ? 'left' : 'center';
                doc.text(data, currentX, currentY, { width: colWidths[i], align });
                currentX += colWidths[i];
            });

            currentY += 12;

            // Add page break if needed
            if (currentY > 700) {
                doc.addPage();
                currentY = 50;
            }
        });

        // Grand Total Summary
        doc.moveDown(1);
        doc.fontSize(14).fillColor('#1f2937').text('GRAND TOTAL SUMMARY', { underline: true });
        doc.moveDown(0.5);

        const summaryY = doc.y;
        doc.fontSize(10);
        doc.text(`Core Subjects Total: ${grandTotalMarks.toFixed(1)} / ${grandTotalMax.toFixed(1)}`, 40, summaryY);
        doc.text(`Additional Subjects: ${additionalMarks.toFixed(1)} / ${additionalMax.toFixed(1)} (Not in Grand Total)`, 40, summaryY + 15);
        doc.text(`Overall Percentage: ${overallPercentage.toFixed(2)}%`, 40, summaryY + 30);
        doc.text(`Overall Grade: ${overallGrade}`, 200, summaryY + 30);
        doc.text(`Promotion Status: ${promotionStatus}`, 350, summaryY + 30);

        // Footer
        doc.fontSize(8).fillColor('#6b7280').text(
            `Generated on ${new Date().toLocaleDateString()} | This is a computer-generated document`,
            40, 750, { align: 'center' }
        );

        doc.end();

        // Wait for PDF to be written and send response
        writeStream.on('finish', () => {
            const publicPath = `/temp/${filename}`;
            res.json({ success: true, url: publicPath });
        });

        writeStream.on('error', (error) => {
            console.error('Error writing PDF:', error);
            res.status(500).json({ error: 'Error generating PDF' });
        });

    } catch (error) {
        console.error('Error generating score card:', error);
        res.status(500).json({ error: 'Error generating score card' });
    }
});

// Academic Performance Analysis
router.get('/analysis', checkExamResultsAccess, async (req, res) => {
    try {
        // Class-wise Analysis
        const [classAnalysis] = await db.execute(`
            SELECT
                s.class,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage,
                MAX((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as highest_percentage,
                MIN((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as lowest_percentage,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 1 END) as pass_count,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 END) as fail_count
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            WHERE s.academic_session = '2023-2024'
            GROUP BY s.class
            ORDER BY s.class
        `);

        // Trade-wise Analysis
        const [tradeAnalysis] = await db.execute(`
            SELECT
                s.trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage,
                MAX((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as highest_percentage,
                MIN((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as lowest_percentage,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 1 END) as pass_count,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 END) as fail_count
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.academic_session = '2023-2024'
            GROUP BY s.trade
            ORDER BY average_percentage DESC
        `);

        // Section-wise Analysis
        const [sectionAnalysis] = await db.execute(`
            SELECT
                CONCAT(s.class, '-', s.section) as class_section,
                s.class,
                s.section,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage,
                MAX((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as highest_percentage,
                MIN((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as lowest_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            WHERE s.academic_session = '2023-2024'
            GROUP BY s.class, s.section
            ORDER BY s.class, s.section
        `);

        // Top Performers
        const [topPerformers] = await db.execute(`
            SELECT
                s.id,
                s.student_id as roll_number,
                s.name,
                s.class,
                s.section,
                s.trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.academic_session = '2023-2024'
            GROUP BY s.id
            ORDER BY average_percentage DESC
            LIMIT 10
        `);

        res.render('exam-results/analysis', {
            title: 'Academic Performance Analysis',
            layout: 'layouts/exam-results',
            currentPage: 'analysis',
            classAnalysis,
            tradeAnalysis,
            sectionAnalysis,
            topPerformers,
            user: req.session.examResultsUser
        });
    } catch (error) {
        console.error('Error loading analysis:', error);
        req.session.flashError = 'Error loading analysis data';
        res.redirect('/exam-results/dashboard');
    }
});

// Score Distribution Analysis
router.get('/score-distribution', checkExamResultsAccess, async (req, res) => {
    try {
        const { trade = 'all', class: classLevel = 'all', section = 'all' } = req.query;

        let whereConditions = ["s.academic_session = '2023-2024'"];
        let queryParams = [];

        if (trade !== 'all') {
            whereConditions.push('s.trade = ?');
            queryParams.push(trade);
        }
        if (classLevel !== 'all') {
            whereConditions.push('s.class = ?');
            queryParams.push(classLevel);
        }
        if (section !== 'all') {
            whereConditions.push('s.section = ?');
            queryParams.push(section);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get student percentages for distribution analysis
        const [studentPercentages] = await db.execute(`
            SELECT
                s.id,
                s.student_id as roll_number,
                s.name,
                s.class,
                s.section,
                s.trade,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            WHERE ${whereClause}
            GROUP BY s.id
            ORDER BY percentage DESC
        `, queryParams);

        // Default score ranges
        const defaultRanges = [
            { min: 90, max: 100, label: '90-100 (Excellent)' },
            { min: 80, max: 89, label: '80-89 (Very Good)' },
            { min: 70, max: 79, label: '70-79 (Good)' },
            { min: 60, max: 69, label: '60-69 (Above Average)' },
            { min: 50, max: 59, label: '50-59 (Average)' },
            { min: 40, max: 49, label: '40-49 (Below Average)' },
            { min: 0, max: 39, label: 'Below 40 (Poor)' }
        ];

        // Calculate distribution
        const distribution = defaultRanges.map(range => {
            const studentsInRange = studentPercentages.filter(student =>
                student.percentage >= range.min && student.percentage <= range.max
            );

            return {
                ...range,
                count: studentsInRange.length,
                percentage: studentPercentages.length > 0 ?
                    Math.round((studentsInRange.length / studentPercentages.length) * 100 * 100) / 100 : 0,
                students: studentsInRange
            };
        });

        // Get filter options
        const [trades] = await db.execute(`
            SELECT DISTINCT trade FROM students WHERE academic_session = '2023-2024' ORDER BY trade
        `);

        const [classes] = await db.execute(`
            SELECT DISTINCT class FROM students WHERE academic_session = '2023-2024' ORDER BY class
        `);

        const [sections] = await db.execute(`
            SELECT DISTINCT section FROM students WHERE academic_session = '2023-2024' ORDER BY section
        `);

        res.render('exam-results/score-distribution', {
            title: 'Score Distribution Analysis',
            layout: 'layouts/exam-results',
            currentPage: 'score-distribution',
            distribution,
            totalStudents: studentPercentages.length,
            filters: { trade, class: classLevel, section },
            trades,
            classes,
            sections,
            user: req.session.examResultsUser
        });
    } catch (error) {
        console.error('Error loading score distribution:', error);
        req.session.flashError = 'Error loading score distribution data';
        res.redirect('/exam-results/dashboard');
    }
});

// Excel export for student results
router.get('/export/excel', checkExamResultsAccess, async (req, res) => {
    try {
        const { trade = 'all', class: classLevel = 'all', section = 'all' } = req.query;

        let whereConditions = ["s.academic_session = '2023-2024'"];
        let queryParams = [];

        if (trade !== 'all') {
            whereConditions.push('s.trade = ?');
            queryParams.push(trade);
        }
        if (classLevel !== 'all') {
            whereConditions.push('s.class = ?');
            queryParams.push(classLevel);
        }
        if (section !== 'all') {
            whereConditions.push('s.section = ?');
            queryParams.push(section);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get student data for export
        const [studentData] = await db.execute(`
            SELECT
                s.id,
                s.student_id as roll_number,
                s.name,
                s.class,
                s.section,
                s.trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as overall_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE ${whereClause}
            GROUP BY s.id
            ORDER BY s.class, s.section, s.name
        `, queryParams);

        // Create Excel workbook
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Student Results');

        // Add headers
        worksheet.columns = [
            { header: 'Student ID', key: 'student_id', width: 15 },
            { header: 'Roll Number', key: 'roll_number', width: 15 },
            { header: 'Student Name', key: 'name', width: 25 },
            { header: 'Class', key: 'class', width: 10 },
            { header: 'Section', key: 'section', width: 10 },
            { header: 'Trade', key: 'trade_full_name', width: 20 },
            { header: 'Overall Percentage', key: 'overall_percentage', width: 18 }
        ];

        // Add data
        studentData.forEach(student => {
            worksheet.addRow({
                student_id: student.id,
                roll_number: student.roll_number,
                name: student.name,
                class: student.class,
                section: student.section,
                trade_full_name: student.trade_full_name,
                overall_percentage: student.overall_percentage
            });
        });

        // Style the header row
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '2563eb' }
        };

        // Set response headers for Excel download
        const filename = `student_results_${Date.now()}.xlsx`;
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

        // Write to response
        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error('Error exporting to Excel:', error);
        res.status(500).json({ error: 'Error exporting data to Excel' });
    }
});

// Logout
router.post('/logout', (req, res) => {
    req.session.examResultsAccess = false;
    req.session.examResultsUser = null;
    req.session.flashSuccess = 'Successfully logged out';
    res.redirect('/exam-results/login');
});

module.exports = router;
