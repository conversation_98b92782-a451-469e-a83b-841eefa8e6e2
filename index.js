const express = require('express');
const session = require('express-session');
const mysql = require('mysql2');
const path = require('path');
const fs = require('fs');
const expressLayouts = require('express-ejs-layouts');
const flash = require('connect-flash');
const cors = require('cors');
const app = express();

// Enable CORS
app.use(cors());

// Database connection
const db = require('./config/database');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Serve files from temp directory explicitly
app.use('/temp', express.static(path.join(__dirname, 'public', 'temp')));

// Configure express-fileupload for handling file uploads
const fileUpload = require('express-fileupload');

// DO NOT apply express-fileupload globally
// Instead, we'll apply it only to specific routes that need file uploads

// Create a reusable express-fileupload middleware for routes that need it
const fileUploadMiddleware = fileUpload({
    useTempFiles: true,
    tempFileDir: '/tmp/',
    limits: {
        fileSize: 20 * 1024 * 1024, // 20MB max file size
        parts: 300 // Increase parts limit for complex forms
    },
    abortOnLimit: true, // Abort on limit to prevent partial uploads
    debug: true, // Enable debugging
    parseNested: true,
    createParentPath: true,
    safeFileNames: true,
    preserveExtension: true,
    uploadTimeout: 60000, // 60 seconds timeout
    responseOnLimit: 'File size limit has been reached (20MB max)',
    abortOnError: true
});

// Create a middleware to handle express-fileupload errors
const handleFileUploadErrors = (err, req, res, next) => {
    if (!err) return next();

    console.error('Express-fileupload error:', err);

    if (err.code === 'ETIMEDOUT') {
        return res.status(408).json({
            success: false,
            message: 'File upload timed out. Please try again with a smaller file.'
        });
    }

    if (err.message && err.message.includes('Unexpected end of form')) {
        console.error('Form parsing error details:', {
            contentType: req.headers['content-type'],
            contentLength: req.headers['content-length'],
            path: req.path,
            method: req.method,
            error: err.toString()
        });

        return res.status(400).json({
            success: false,
            message: 'File upload was interrupted or corrupted. Please try again with a smaller file or different format.'
        });
    }

    return res.status(500).json({
        success: false,
        message: 'File upload error: ' + err.message
    });
};

// Session configuration with optimized settings
app.use(session({
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,        // Only save session when data changes
    saveUninitialized: false, // Don't create session until something stored
    rolling: true,        // Reset expiration on activity
    cookie: {
        secure: false,    // Set to false for development (no HTTPS)
        httpOnly: true,   // Prevent client-side JS from reading cookie
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: 'lax'   // Prevent CSRF
    },
    // Reduce database writes by only saving session every 5 minutes
    // unless there are important changes
    touchAfter: 5 * 60 // 5 minutes in seconds
}));

// Initialize flash middleware
app.use(flash());

// Add request context middleware to store the current request in a global variable
const requestContextMiddleware = require('./middleware/request-context');
app.use(requestContextMiddleware);

// Add session debug middleware
app.use((req, res, next) => {
    console.log('Session Debug:', {
        id: req.sessionID,
        userId: req.session.userId,
        userRole: req.session.userRole,
        username: req.session.username,
        path: req.path,
        method: req.method
    });
    next();
});

// Query logger middleware disabled due to performance issues
// const { queryLoggerMiddleware } = require('./middleware/query-logger');
// app.use(queryLoggerMiddleware);

// Add session manager middleware to prevent multi-device login
const { sessionManager } = require('./middleware/session-manager');
app.use(sessionManager);

// Add logger middleware to log user actions
const logUserAction = require('./middleware/logger-middleware');
app.use(logUserAction);

// Add JavaScript variable logger middleware
const jsVariableLoggerMiddleware = require('./middleware/js-variable-logger');
app.use(jsVariableLoggerMiddleware);

// Add JS Logger middleware for logjsvalues function
const jsLoggerMiddleware = require('./middleware/js-logger-middleware');
app.use(jsLoggerMiddleware);

// Add test mode middleware to disable chat during tests
const { checkTestMode } = require('./middleware/test-mode-middleware');
app.use(checkTestMode);

// Add i18n middleware for language support
const i18n = require('./config/i18n');
const cookieParser = require('cookie-parser');
app.use(cookieParser());
app.use(i18n.init);

// Add language middleware to make language available in all views
app.use((req, res, next) => {
    // Make i18n functions available in templates
    res.locals.__ = req.__ || ((text) => text);
    res.locals.__n = req.__n || ((singular, plural, count) => count === 1 ? singular : plural);

    // Make current language available in templates
    res.locals.currentLanguage = req.getLocale ? req.getLocale() : 'en';
    res.locals.availableLanguages = i18n.getLocales ? i18n.getLocales() : ['en', 'pa'];

    // Store i18n in app.locals for access in routes
    app.locals.i18n = i18n;

    next();
});

// View engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');

// Setup express-ejs-layouts
app.use(expressLayouts);
app.set('layout', 'layouts/default');
app.set("layout extractScripts", true);
app.set("layout extractStyles", true);

// Add layout path middleware
const layoutPathMiddleware = require('./middleware/layout-path-middleware');
app.use(layoutPathMiddleware);

// Add contentFor middleware to make contentFor function available in all views
const contentForMiddleware = require('./middleware/content-for-middleware');
app.use(contentForMiddleware);

// Import middleware
const { checkAuthenticated, checkAdmin, isAdmin, checkITAdmin, isITAdmin } = require('./middleware/auth');
const checkScheduledTests = require('./middleware/scheduled-tests');

// Helper function to check if request is API/curl
app.use((req, res, next) => {
    req.isApiRequest = req.xhr ||
        (req.headers.accept && req.headers.accept.indexOf('json') > -1) ||
        (req.headers['user-agent'] && req.headers['user-agent'].toLowerCase().includes('curl'));
    next();
});

// Toast and Flash message middleware
app.use((req, res, next) => {
    try {
        // Debug session and locals
        console.log('Toast middleware - Session:', {
            sessionID: req.sessionID,
            toast: req.session?.toast,
            flashSuccess: req.session?.flashSuccess,
            flashError: req.session?.flashError
        });

        // Initialize toast in locals if it doesn't exist
        res.locals.toast = res.locals.toast || null;

        // Handle direct toast messages (new approach)
        if (req.session && req.session.toast) {
            console.log('Setting toast from session.toast:', req.session.toast);
            res.locals.toast = req.session.toast;

            try {
                delete req.session.toast;
            } catch (e) {
                console.error('Error deleting toast from session:', e);
            }
        }

        // Handle legacy flash messages (backward compatibility)
        if (req.session && (req.session.flashSuccess || req.session.flashError)) {
            // Convert flash messages to toast format
            if (!res.locals.toast) {
                console.log('Setting toast from flash messages');
                res.locals.toast = {
                    message: req.session.flashSuccess || req.session.flashError,
                    type: req.session.flashSuccess ? 'success' : 'error'
                };
            }

            // Also set flash locals for backward compatibility
            res.locals.flashSuccess = req.session.flashSuccess;
            res.locals.flashError = req.session.flashError;

            // Clear flash messages from session
            try {
                delete req.session.flashSuccess;
                delete req.session.flashError;
            } catch (e) {
                console.error('Error deleting flash messages from session:', e);
            }
        }

        // Debug the final toast object
        console.log('Toast middleware - Final locals.toast:', res.locals.toast);
    } catch (error) {
        console.error('Error in toast middleware:', error);
    }

    next();
});

// Check for scheduled tests that need to be published
app.use(checkScheduledTests);

// Add user to res.locals for templates and check admin status
app.use(isAdmin);
app.use(async (req, res, next) => {
    if (req.session.userId) {
        // Get notification count
        let notificationCount = 0;
        try {
            const [result] = await db.query(
                'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0',
                [req.session.userId]
            );
            notificationCount = result[0].count;
        } catch (error) {
            console.warn('Error fetching notification count:', error.message);
            // Table might not exist yet, use default value of 0
        }

        res.locals.user = {
            id: req.session.userId,
            username: req.session.username || 'User',
            role: req.session.userRole || 'user',
            notifications: []
        };
        res.locals.notificationCount = notificationCount;
        } else {
        res.locals.user = {
            name: 'Guest',
            role: 'guest',
            notifications: []
        };
        res.locals.notificationCount = 0;
    }

    // Load site settings
    try {
        const settingsController = require('./controllers/settings-controller');
        const siteSettings = await settingsController.getSiteSettings();
        res.locals.siteSettings = siteSettings;
    } catch (error) {
        console.warn('Error loading site settings:', error.message);
        res.locals.siteSettings = {};
    }

    // Set default navbar
    res.locals.user = res.locals.user || {};
    res.locals.currentPage = 'tests';

    // Set default layout based on user role and path
    if (req.session.userId) {
        if (req.session.userRole === 'admin' && req.path.startsWith('/admin')) {
            res.locals.layout = 'admin';
        } else if ((req.session.userRole === 'principal' || req.session.userRole === 'admin') && req.path.startsWith('/principal')) {
            res.locals.layout = 'principal';
        } else if (req.session.userRole === 'it_admin' && req.path.startsWith('/it-admin')) {
            res.locals.layout = 'it-admin';
        } else if ((req.session.userRole === 'teacher' || req.session.userRole === 'admin') && req.path.startsWith('/teacher')) {
            res.locals.layout = 'teacher';
        } else if (req.session.userRole === 'student' && req.path.startsWith('/student')) {
            res.locals.layout = 'student';
        } else {
            res.locals.layout = 'user';
        }
    } else {
        res.locals.layout = 'user';
    }

    next();
});

// Import and register routes

// Language routes (no auth required)
try {
    const languageRoutes = require('./routes/language-routes');
    app.use('/language', languageRoutes);
    console.log('Language routes registered');
} catch (error) {
    console.error('Error loading language routes:', error);
}

try {
    const authRoutes = require('./routes/auth-routes');
    app.use(authRoutes); // Auth routes should be first

    // Register demo login routes
    const demoLoginRoutes = require('./routes/demo-login-routes');
    app.use(demoLoginRoutes);
    console.log('Demo login routes registered');
} catch (error) {
    console.error('Error loading auth routes:', error);
    process.exit(1); // Exit if auth routes can't be loaded
}

try {
    const adminRoutes = require('./routes/admin-routes');
    app.use('/admin', checkAuthenticated, checkAdmin, adminRoutes);
    console.log('Admin routes registered');

    // Register admin user routes
    const adminUserRoutes = require('./routes/admin-user-routes');
    app.use('/admin/users', checkAuthenticated, checkAdmin, adminUserRoutes);
    console.log('Admin user routes registered');

    // Register student import routes
    const studentImportRoutes = require('./routes/student-import-routes');
    app.use('/admin/students', checkAuthenticated, checkAdmin, studentImportRoutes);
    console.log('Student import routes registered');
} catch (error) {
    console.error('Error loading admin routes:', error);
}

try {
    const questionsRoutes = require('./routes/questions-routes');
    app.use('/admin/questions', checkAuthenticated, checkAdmin, questionsRoutes);
    console.log('Questions routes registered');
} catch (error) {
    console.error('Error loading questions routes:', error);
}

try {
    const imageRoutes = require('./routes/image-routes');
    app.use('/admin/images', checkAuthenticated, checkAdmin, imageRoutes);
    console.log('Image routes registered');
} catch (error) {
    console.error('Error loading image routes:', error);
}

try {
    const categoryRoutes = require('./routes/category-routes');
    app.use('/', categoryRoutes);
    console.log('Category routes registered');
} catch (error) {
    console.error('Error loading category routes:', error);
}

try {
    const settingsRoutes = require('./routes/settings-routes');
    app.use('/admin/settings', checkAuthenticated, checkAdmin, settingsRoutes);
    console.log('Settings routes registered');
} catch (error) {
    console.error('Error loading settings routes:', error);
}

try {
    const reportsRoutes = require('./routes/reports-routes');
    app.use('/admin/reports', checkAuthenticated, checkAdmin, reportsRoutes);
    console.log('Reports routes registered');
} catch (error) {
    console.error('Error loading reports routes:', error.stack);
}

try {
    const roleApiRoutes = require('./routes/api/role-api');
    app.use('/admin/api', checkAuthenticated, checkAdmin, roleApiRoutes);
    console.log('Role API routes registered');
} catch (error) {
    console.error('Error loading role API routes:', error.stack);
}

try {
    const testRoutes = require('./routes/test-routes');
    app.use('/tests', checkAuthenticated, testRoutes);
} catch (error) {
    console.error('Error loading test routes:', error);
}

try {
    const uploadRoutes = require('./routes/upload-routes');
    app.use('/upload', checkAuthenticated, uploadRoutes);
    console.log('Upload routes registered');
} catch (error) {
    console.error('Error loading upload routes:', error);
}

try {
    const notificationModule = require('./routes/notification-routes');
    const notificationRoutes = notificationModule;
    const createNotification = notificationModule.createNotification;
    app.use('/notifications', checkAuthenticated, notificationRoutes);
} catch (error) {
    console.error('Error loading notification routes:', error);
}

try {
    const profileRoutes = require('./routes/profile-routes');
    app.use('/profile', checkAuthenticated, profileRoutes);
} catch (error) {
    console.error('Error loading profile routes:', error);
}

try {
    const profileUpdateRoutes = require('./routes/profile-update-routes');
    app.use('/profile-update', checkAuthenticated, profileUpdateRoutes);
    console.log('Profile update routes registered');
} catch (error) {
    console.error('Error loading profile update routes:', error);
}

try {
    const apiRoutes = require('./routes/api-routes');
    app.use('/api', apiRoutes);
    console.log('API routes registered');

    // Register user API routes directly
    const userApiRoutes = require('./routes/api/user-api');
    app.use('/api/users', userApiRoutes);
    console.log('User API routes registered');

    // Register student API routes directly
    const studentApiRoutes = require('./routes/api/student-api');
    app.use('/api/student', studentApiRoutes);
    console.log('Student API routes registered directly');

    // API routes for notifications are registered through the API router

    // API routes for chat are registered through the API router

    // API route for recent chats
    app.get('/api/chat/recent', checkAuthenticated, async (req, res) => {
        try {
            // Get recent private chats
            const [chats] = await db.query(`
                SELECT
                    u.id as user_id,
                    u.username,
                    u.profile_image,
                    (
                        SELECT message
                        FROM chat_messages
                        WHERE (sender_id = ? AND recipient_id = u.id) OR (sender_id = u.id AND recipient_id = ?)
                        ORDER BY created_at DESC
                        LIMIT 1
                    ) as last_message,
                    (
                        SELECT created_at
                        FROM chat_messages
                        WHERE (sender_id = ? AND recipient_id = u.id) OR (sender_id = u.id AND recipient_id = ?)
                        ORDER BY created_at DESC
                        LIMIT 1
                    ) as last_message_time,
                    (
                        SELECT COUNT(*)
                        FROM chat_messages
                        WHERE sender_id = u.id AND recipient_id = ? AND is_read = 0
                    ) as unread_count
                FROM users u
                WHERE u.id IN (
                    SELECT DISTINCT
                        CASE
                            WHEN sender_id = ? THEN recipient_id
                            ELSE sender_id
                        END as user_id
                    FROM chat_messages
                    WHERE sender_id = ? OR recipient_id = ?
                )
                ORDER BY last_message_time DESC
                LIMIT 10
            `, [
                req.session.userId, req.session.userId,
                req.session.userId, req.session.userId,
                req.session.userId,
                req.session.userId, req.session.userId, req.session.userId
            ]);

            res.json({
                success: true,
                chats
            });
        } catch (error) {
            console.error('Error fetching recent chats:', error);
            res.status(500).json({
                success: false,
                message: 'Error fetching recent chats'
            });
        }
    });

    // API route for groups
    app.get('/api/chat/groups', checkAuthenticated, async (req, res) => {
        try {
            // Get groups
            const [groups] = await db.query(`
                SELECT
                    g.id as group_id,
                    g.name,
                    (
                        SELECT message
                        FROM group_messages
                        WHERE group_id = g.id
                        ORDER BY created_at DESC
                        LIMIT 1
                    ) as last_message,
                    (
                        SELECT created_at
                        FROM group_messages
                        WHERE group_id = g.id
                        ORDER BY created_at DESC
                        LIMIT 1
                    ) as last_message_time,
                    (
                        SELECT username
                        FROM users
                        WHERE id = (
                            SELECT sender_id
                            FROM group_messages
                            WHERE group_id = g.id
                            ORDER BY created_at DESC
                            LIMIT 1
                        )
                    ) as last_sender,
                    (
                        SELECT COUNT(*)
                        FROM group_messages gm
                        LEFT JOIN group_message_reads gmr ON gm.message_id = gmr.message_id AND gmr.user_id = ?
                        WHERE gm.group_id = g.id AND gmr.read_at IS NULL AND gm.sender_id != ?
                    ) as unread_count
                FROM groups g
                INNER JOIN group_members gm ON g.id = gm.group_id
                WHERE gm.user_id = ?
                ORDER BY last_message_time DESC
            `, [req.session.userId, req.session.userId, req.session.userId]);

            res.json({
                success: true,
                groups
            });
        } catch (error) {
            console.error('Error fetching groups:', error);
            res.status(500).json({
                success: false,
                message: 'Error fetching groups'
            });
        }
    });

    // API route for user search
    app.get('/api/users/search', checkAuthenticated, async (req, res) => {
        try {
            const { q } = req.query;

            if (!q || q.length < 2) {
                return res.json({
                    success: true,
                    users: []
                });
            }

            // Search users - explicitly select all needed fields
            const [users] = await db.query(`
                SELECT
                    id,
                    username,
                    email AS user_email,
                    profile_image,
                    role
                FROM users
                WHERE (username LIKE ? OR email LIKE ? OR name LIKE ?)
                AND id != ?
                LIMIT 10
            `, [`%${q}%`, `%${q}%`, `%${q}%`, req.session.userId]);

            // Transform the results to ensure email is available
            const transformedUsers = users.map(user => ({
                id: user.id,
                username: user.username,
                email: user.user_email, // Use the aliased field
                profile_image: user.profile_image,
                role: user.role
            }));

            // Log the transformed users being returned
            console.log('API users/search response:', {
                success: true,
                users: transformedUsers
            });

            res.json({
                success: true,
                users: transformedUsers
            });
        } catch (error) {
            console.error('Error searching users:', error);
            res.status(500).json({
                success: false,
                message: 'Error searching users'
            });
        }
    });

    // API route for creating groups
    app.post('/api/chat/groups/create', checkAuthenticated, async (req, res) => {
        try {
            const { name, members } = req.body;

            if (!name) {
                return res.status(400).json({
                    success: false,
                    message: 'Group name is required'
                });
            }

            if (!members || !Array.isArray(members) || members.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'At least one member is required'
                });
            }

            // Start transaction
            await db.query('START TRANSACTION');

            // Create group
            const [result] = await db.query(
                'INSERT INTO groups (name, created_by, created_at) VALUES (?, ?, NOW())',
                [name, req.session.userId]
            );

            const groupId = result.insertId;

            // Add creator as admin
            await db.query(
                'INSERT INTO group_members (group_id, user_id, is_admin, joined_at) VALUES (?, ?, 1, NOW())',
                [groupId, req.session.userId]
            );

            // Add members
            for (const memberId of members) {
                if (memberId != req.session.userId) {
                    await db.query(
                        'INSERT INTO group_members (group_id, user_id, is_admin, joined_at) VALUES (?, ?, 0, NOW())',
                        [groupId, memberId]
                    );
                }
            }

            // Commit transaction
            await db.query('COMMIT');

            res.json({
                success: true,
                groupId
            });
        } catch (error) {
            // Rollback transaction on error
            await db.query('ROLLBACK');
            console.error('Error creating group:', error);
            res.status(500).json({
                success: false,
                message: 'Error creating group'
            });
        }
    });

    // API routes for admin notifications
    app.get('/api/admin-notifications/active', async (req, res) => {
        try {
            // Get active admin notifications
            const [notifications] = await db.query(`
                SELECT * FROM admin_notifications
                WHERE is_active = 1
                AND (start_date IS NULL OR start_date <= NOW())
                AND (end_date IS NULL OR end_date >= NOW())
                ORDER BY created_at DESC
            `);

            res.json({
                success: true,
                notifications
            });
        } catch (error) {
            console.error('Error fetching active admin notifications:', error);
            res.status(500).json({
                success: false,
                message: 'Error fetching active admin notifications'
            });
        }
    });
} catch (error) {
    console.error('Error loading API routes:', error);
}

try {
    const debugRoutes = require('./routes/debug-routes');
    app.use('/debug', debugRoutes);
    console.log('Debug routes registered');
} catch (error) {
    console.error('Error loading debug routes:', error);
}

try {
    const helpRoutes = require('./routes/help-routes');
    app.use('/help', helpRoutes);
    console.log('Help routes registered');
} catch (error) {
    console.error('Error loading help routes:', error);
}

try {
    const groupRoutes = require('./routes/group-routes');
    app.use('/groups', checkAuthenticated, groupRoutes);
    console.log('Group routes registered');
} catch (error) {
    console.error('Error loading group routes:', error);
}

try {
    const chatRoutes = require('./routes/chat-routes');
    app.use('/chat', chatRoutes);
    console.log('Chat routes registered');

    // Admin chat monitoring routes
    const chatController = require('./controllers/chat-controller');
    app.get('/admin/chat/monitor', checkAdmin, chatController.adminMonitor);
    app.get('/admin/chat/private/:user1Id/:user2Id', checkAdmin, chatController.adminViewPrivateChat);
    app.get('/admin/chat/group/:groupId', checkAdmin, chatController.adminViewGroupChat);
    console.log('Admin chat monitoring routes registered');
} catch (error) {
    console.error('Error loading chat routes:', error);
}

try {
    const adminNotificationRoutes = require('./routes/admin-notification-routes');
    app.use('/admin/notifications', checkAdmin, adminNotificationRoutes);
    console.log('Admin notification routes registered');
} catch (error) {
    console.error('Error loading admin notification routes:', error);
}

try {
    const adminTestAssignmentsRoutes = require('./routes/admin-test-assignments-routes');
    app.use('/admin/test-assignments', checkAuthenticated, checkAdmin, adminTestAssignmentsRoutes);
    console.log('Admin test assignments routes registered');
} catch (error) {
    console.error('Error loading admin test assignments routes:', error);
}

try {
    const adminErrorLogsRoutes = require('./routes/admin-error-logs-routes');
    app.use('/admin/error-logs', checkAuthenticated, checkAdmin, adminErrorLogsRoutes);
    console.log('Admin error logs routes registered');
} catch (error) {
    console.error('Error loading admin error logs routes:', error);
}

try {
    // Special handling for query logs routes - disable express-fileupload
    const adminQueryLogsRoutes = require('./routes/admin-query-logs-routes');

    // Create a middleware to disable express-fileupload for query logs
    const disableFileUpload = (req, res, next) => {
        // Set a flag to indicate that this route should not use express-fileupload
        req.skipFileUpload = true;
        next();
    };

    // Apply the middleware before the routes
    app.use('/admin/query-logs', disableFileUpload, checkAuthenticated, checkAdmin, adminQueryLogsRoutes);
    console.log('Admin query logs routes registered with file upload disabled');
} catch (error) {
    console.error('Error loading admin query logs routes:', error);
}

try {
    const inventoryRoutes = require('./routes/inventory-routes');
    app.use('/admin/inventory', checkAuthenticated, checkAdmin, inventoryRoutes);
    console.log('Inventory routes registered');

    // Register inventory image routes
    const inventoryImageRoutes = require('./routes/inventory-image-routes');
    app.use('/admin/inventory/images', checkAuthenticated, checkAdmin, inventoryImageRoutes);
    console.log('Inventory image routes registered');

    // Register hardware condition routes
    const hardwareConditionRoutes = require('./routes/hardware-condition-routes');
    app.use('/admin/inventory/condition', checkAuthenticated, checkAdmin, hardwareConditionRoutes);
    console.log('Hardware condition routes registered');
} catch (error) {
    console.error('Error loading inventory routes:', error);
}

try {
    // Register issue tracker routes
    const issueTrackerRoutes = require('./routes/issue-tracker-routes');
    app.use('/issues', checkAuthenticated, issueTrackerRoutes);
    console.log('Issue tracker routes registered');

    // Register admin issue routes
    const adminIssueRoutes = require('./routes/admin-issue-routes');
    app.use('/admin/issues', checkAuthenticated, checkAdmin, adminIssueRoutes);
    console.log('Admin issue routes registered');
} catch (error) {
    console.error('Error loading issue tracker routes:', error);
}

try {
    // Register repair vendor routes
    const repairVendorRoutes = require('./routes/repair-vendor-routes');
    app.use('/admin/repair', checkAuthenticated, checkAdmin, repairVendorRoutes);
    console.log('Repair vendor routes registered');
} catch (error) {
    console.error('Error loading repair vendor routes:', error);
}

// Teacher routes
const teacherRoutes = require('./routes/teacher-routes');
app.use('/teacher', checkAuthenticated, teacherRoutes);
console.log('Teacher Routes registered');

// Teacher API routes
try {
    const teacherApiRoutes = require('./routes/api/teacher-api');
    app.use('/api/teacher', teacherApiRoutes);
    console.log('Teacher API routes registered');

    // Teacher Profile API routes
    const teacherProfileApiRoutes = require('./routes/api/teacher-profile-api');
    app.use('/api/teacher', teacherProfileApiRoutes);
    console.log('Teacher Profile API routes registered');
} catch (error) {
    console.error('Error loading teacher API routes:', error);
}

// IT Admin routes
try {
    const itAdminRoutes = require('./routes/it-admin-routes');

    // Create a special router for inventory routes with fileUploadMiddleware
    const inventoryRouter = express.Router();
    app.use('/it-admin/inventory', checkAuthenticated, checkITAdmin, (req, res, next) => {
        // Apply fileUploadMiddleware to specific routes
        if (req.path === '/add' ||
            req.path.match(/\/\d+\/edit/) ||
            req.path === '/preview-import' ||
            req.path === '/import') {
            console.log('Applying fileUploadMiddleware to inventory route:', req.path);
            return fileUploadMiddleware(req, res, next);
        }
        next();
    });

    // Register the main IT Admin routes
    app.use('/it-admin', checkAuthenticated, checkITAdmin, itAdminRoutes);

    console.log('IT Admin Routes registered with file upload middleware for inventory routes');
} catch (error) {
    console.error('Error loading IT Admin routes:', error);
}

// Student routes
try {
    const studentRoutes = require('./routes/student-routes');
    app.use('/student', checkAuthenticated, studentRoutes);
    console.log('Student Routes registered');
} catch (error) {
    console.error('Error loading Student routes:', error);
}

// Principal routes
try {
    const principalRoutes = require('./routes/principal-routes');
    app.use('/principal', checkAuthenticated, principalRoutes);
    console.log('Principal Routes registered');
} catch (error) {
    console.error('Error loading Principal routes:', error);
}

// Exam Results routes
try {
    const examResultsRoutes = require('./routes/exam-results-routes');
    app.use('/exam-results', examResultsRoutes);  // Has its own authentication
    console.log('Exam Results Routes registered');
} catch (error) {
    console.error('Error loading Exam Results routes:', error);
}

// Test route for test assignments
app.get('/test-assignments-test', (req, res) => {
    res.send('Test assignments route is working!');
});

// Root route
app.get('/', (req, res) => {
    if (req.session.userId) {
        // Redirect based on user role
        switch (req.session.userRole) {
            case 'admin':
                return res.redirect('/admin/dashboard');
            case 'principal':
                return res.redirect('/principal/dashboard');
            case 'it_admin':
                return res.redirect('/it-admin/dashboard');
            case 'teacher':
                return res.redirect('/teacher/dashboard');
            case 'student':
                return res.redirect('/student/dashboard');
            default:
                return res.redirect('/tests');
        }
    } else {
        return res.redirect('/login');
    }
});

// 404 handler
app.use((req, res, next) => {
    const error = new Error('Page Not Found');
    error.status = 404;
    next(error);
});

// Error handling middleware
app.use(async (err, req, res, next) => {
    console.error('Error:', err);

    // Special handling for query-logs routes
    if (req.path.includes('/admin/query-logs')) {
        console.log('Ignoring error for query-logs route:', err.message);
        return next();
    }

    // Handle express-fileupload specific errors
    if (err && err.code === 'EBADCSRFTOKEN') {
        err.message = 'Invalid CSRF token. Please refresh the page and try again.';
        err.status = 403; // Forbidden
    } else if (err && err.code === 'LIMIT_FILE_SIZE') {
        err.message = 'File is too large. Maximum size is 20MB.';
        err.status = 413; // Payload Too Large
    } else if (err && err.message && err.message.includes('Unexpected end of form')) {
        console.error('Form parsing error details:', {
            contentType: req.headers['content-type'],
            contentLength: req.headers['content-length'],
            path: req.path,
            method: req.method,
            error: err.toString()
        });

        // Create a more user-friendly error message
        err.message = 'File upload was interrupted or corrupted. Please try again with a smaller file or different format.';
        err.status = 400; // Bad Request
    }

    // Log the error to the logs table
    try {
        const { logEvent } = require('./utils/logger');
        await logEvent(
            req,
            'error',
            'system',
            'Application Error',
            `Error occurred: ${err.message}`,
            'error',
            err.stack
        );
    } catch (logError) {
        console.error('Failed to log error:', logError);
    }

    const error = {
        status: err.status || 500,
        stack: process.env.NODE_ENV === 'development' ? err.stack : ''
    };

    const message = err.message || 'Internal Server Error';

    // Check if this is an API request, procurement request, or AJAX request
    const isApiOrProcurementRequest =
        req.isApiRequest ||
        req.path.includes('/api/') ||
        req.path.includes('/procurement/') ||
        req.xhr ||
        (req.headers.accept && req.headers.accept.includes('application/json')) ||
        (req.headers['content-type'] && req.headers['content-type'].includes('multipart/form-data'));

    // If it's an API or procurement request, return JSON
    if (isApiOrProcurementRequest) {
        // Set the content type to JSON
        res.setHeader('Content-Type', 'application/json');

        // Return a JSON response
        return res.status(error.status).json({
            success: false,
            error: message,
            details: process.env.NODE_ENV === 'development' ? err.stack : undefined
        });
    }

    // For regular requests, render the error page
    res.status(error.status);
    res.render('error', {
        message,
        error,
        title: `Error ${error.status}`,
        navbar: 'tests'
    });
});

// Create required directories
const uploadDir = path.join(__dirname, 'public', 'uploads', 'profiles');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Function to log system events
const logSystemEvent = async (level, operation, details, status = 'success', errorMessage = null) => {
    try {
        // Create a mock request object
        const mockReq = {
            method: 'SYSTEM',
            path: '/system',
            originalUrl: '/system',
            ip: '127.0.0.1',
            get: () => 'System',
            session: {}
        };

        const { logEvent } = require('./utils/logger');
        await logEvent(
            mockReq,
            level,
            'system',
            operation,
            details,
            status,
            errorMessage
        );
    } catch (error) {
        console.error('Failed to log system event:', error);
    }
};

// Start server with error handling
const PORT = process.env.PORT || 3019;
const server = require('http').createServer(app);

// Initialize Socket.io
const io = require('socket.io')(server);

// Socket.io connection handling
require('./sockets/socket-handler')(io);

// Initialize WebSocket server
const { initWebSocketServer } = require('./websocket-server');
const wss = initWebSocketServer(server);

// Start the server
server.listen(PORT, async () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`Server URL: http://localhost:${PORT}`);

    // Log server start event
    await logSystemEvent(
        'info',
        'Server Start',
        `Server started on port ${PORT}`,
        'success'
    );
    console.log('Available routes:');
    console.log('- GET  /login');
    console.log('- POST /login');
    console.log('- GET  /register');
    console.log('- POST /register');
    console.log('- GET  /logout');
    console.log('- GET  /tests');
    console.log('- GET  /profile');
    console.log('- GET  /admin/dashboard (admin only)');
});

// Handle server errors
server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
        console.error(`Port ${PORT} is already in use. Please close other applications using this port or specify a different port.`);
    } else {
        console.error('Server error:', error);
    }
    process.exit(1);
});